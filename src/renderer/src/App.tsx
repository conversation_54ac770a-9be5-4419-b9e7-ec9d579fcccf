import React from 'react'
import { ThemeProvider, AntdProvider } from '@renderer/contexts'
import Router from './Router'
import { loggerService } from '@logger'
import TopViewContainer from './components/TopView'

const logger = loggerService.withContext('App.tsx')

function App(): React.JSX.Element {
  logger.info('App initialized')

  return (
    <ThemeProvider>
      <AntdProvider>
        <TopViewContainer>
          <Router />
        </TopViewContainer>
      </AntdProvider>
    </ThemeProvider>
  )
}

export default App

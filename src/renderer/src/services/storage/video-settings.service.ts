/**
 * 视频级别设置服务 / Video-Specific Settings Service
 *
 * 管理每个视频文件的独立设置，包括播放速度、音量、字幕配置等
 * Manages independent settings for each video file including playback speed, volume, subtitle configuration, etc.
 */

import { logger } from '@renderer/utils/logger'
import { IPCClientService } from '../api'

/**
 * 字幕显示设置 / Subtitle Display Settings
 */
export interface SubtitleDisplaySettings {
  /** 是否显示字幕 / Whether to show subtitles */
  enabled: boolean

  /** 字幕显示模式 / Subtitle display mode */
  displayMode: 'original' | 'translation' | 'bilingual'

  /** 字幕字体大小 / Subtitle font size */
  fontSize: number

  /** 字幕位置 / Subtitle position */
  position: 'bottom' | 'center' | 'top'

  /** 字幕背景透明度 / Subtitle background opacity */
  backgroundOpacity: number

  /** 字幕文字颜色 / Subtitle text color */
  textColor: string

  /** 字幕背景颜色 / Subtitle background color */
  backgroundColor: string
}

/**
 * 循环播放设置 / Loop Settings
 */
export interface LoopSettings {
  /** 是否启用单句循环 / Whether to enable single sentence loop */
  isSingleLoop: boolean

  /** 循环开始时间 / Loop start time */
  startTime?: number

  /** 循环结束时间 / Loop end time */
  endTime?: number

  /** 循环间隔时间(毫秒) / Loop interval in milliseconds */
  intervalMs: number

  /** 循环次数(-1为无限循环) / Loop count (-1 for infinite) */
  loopCount: number
}

/**
 * 视频播放设置 / Video Playback Settings
 */
export interface VideoPlaybackSettings {
  /** 播放速度 / Playback speed */
  playbackRate: number

  /** 音量 / Volume */
  volume: number

  /** 是否自动播放 / Auto play */
  autoPlay: boolean

  /** 是否自动暂停 / Auto pause */
  isAutoPause: boolean

  /** 显示模式 / Display mode */
  displayMode: 'original' | 'translation' | 'bilingual'

  /** 用户选择的播放速度选项 / User selected playback speed options */
  selectedPlaybackRates: number[]

  /** 循环设置 / Loop settings */
  loopSettings: LoopSettings

  /** 字幕显示设置 / Subtitle display settings */
  subtitleDisplay: SubtitleDisplaySettings

  /** 字幕布局是否锁定 / Whether subtitle layout is locked */
  isSubtitleLayoutLocked: boolean
}

/**
 * 视频UI配置 / Video UI Configuration
 */
export interface VideoUIConfig {
  /** 是否显示控制栏 / Whether to show control bar */
  showControlBar: boolean

  /** 是否显示字幕列表 / Whether to show subtitle list */
  showSubtitleList: boolean

  /** 是否显示播放页面头部 / Whether to show play page header */
  showPlayPageHeader: boolean

  /** 最后播放位置 / Last play position */
  lastPosition: number

  /** 当前字幕索引 / Current subtitle index */
  currentSubtitleIndex: number

  /** 窗口状态 / Window state */
  windowState: {
    /** 是否全屏 / Is fullscreen */
    isFullscreen: boolean

    /** 窗口尺寸 / Window size */
    windowSize?: {
      width: number
      height: number
    }
  }
}

/**
 * 完整的视频设置 / Complete Video Settings
 */
export interface VideoSettings {
  /** 视频文件ID / Video file ID */
  fileId: string

  /** 视频文件路径 / Video file path */
  filePath: string

  /** 播放设置 / Playback settings */
  playback: VideoPlaybackSettings

  /** UI配置 / UI configuration */
  ui: VideoUIConfig

  /** 最后更新时间 / Last updated timestamp */
  lastUpdated: number

  /** 版本号 / Version number */
  version: string
}

/**
 * 视频设置更新选项 / Video Settings Update Options
 */
export interface VideoSettingsUpdateOptions {
  /** 是否立即保存 / Whether to save immediately */
  saveImmediately?: boolean

  /** 是否验证数据 / Whether to validate data */
  validate?: boolean

  /** 是否合并设置 / Whether to merge settings */
  merge?: boolean
}

/**
 * 视频设置服务类 / Video Settings Service Class
 */
export class VideoSettingsService {
  private readonly storageKey = 'video-settings'
  private ipcClient: IPCClientService
  private settingsCache = new Map<string, VideoSettings>()

  constructor() {
    this.ipcClient = new IPCClientService()
  }

  /**
   * 获取视频设置 / Get video settings
   */
  async getVideoSettings(fileId: string, filePath: string): Promise<VideoSettings> {
    try {
      logger.info(`⚙️ 正在获取视频设置: ${fileId}`)

      // 检查缓存 / Check cache
      if (this.settingsCache.has(fileId)) {
        const cached = this.settingsCache.get(fileId)!
        logger.info(`🎯 从缓存获取视频设置: ${fileId}`)
        return cached
      }

      // 从存储获取所有视频设置 / Get all video settings from storage
      const allSettingsResult = await this.ipcClient.storage.getRawData(this.storageKey)
      const allSettings =
        allSettingsResult.success && allSettingsResult.data
          ? JSON.parse(allSettingsResult.data)
          : {}
      const videoSettings = allSettings[fileId]

      if (videoSettings && this.isValidVideoSettings(videoSettings)) {
        const migratedSettings = this.migrateVideoSettings(videoSettings)
        this.settingsCache.set(fileId, migratedSettings)
        logger.info(`✅ 视频设置获取成功: ${fileId}`)
        return migratedSettings
      }

      // 如果没有存储的设置，使用默认设置 / Use default settings if no stored settings
      const defaultSettings = this.getDefaultVideoSettings(fileId, filePath)
      await this.saveVideoSettings(defaultSettings, { saveImmediately: true })

      logger.info(`✅ 使用默认视频设置: ${fileId}`)
      return defaultSettings
    } catch (error) {
      logger.error(`❌ 获取视频设置失败: ${fileId}`, error)
      return this.getDefaultVideoSettings(fileId, filePath)
    }
  }

  /**
   * 保存视频设置 / Save video settings
   */
  async saveVideoSettings(
    settings: VideoSettings,
    options: VideoSettingsUpdateOptions = {}
  ): Promise<boolean> {
    try {
      const { saveImmediately = true, validate = true } = options

      logger.info(`💾 正在保存视频设置: ${settings.fileId}`)

      // 验证设置 / Validate settings
      if (validate && !this.isValidVideoSettings(settings)) {
        throw new Error('Invalid video settings data')
      }

      // 更新时间戳 / Update timestamp
      const updatedSettings: VideoSettings = {
        ...settings,
        lastUpdated: Date.now(),
        version: '2.0.0'
      }

      if (saveImmediately) {
        // 获取所有设置 / Get all settings
        const allSettingsResult = await this.ipcClient.storage.getRawData(this.storageKey)
        const allSettings =
          allSettingsResult.success && allSettingsResult.data
            ? JSON.parse(allSettingsResult.data)
            : {}

        // 更新特定视频的设置 / Update specific video settings
        allSettings[settings.fileId] = updatedSettings

        // 保存回存储 / Save back to storage
        const saveResult = await this.ipcClient.storage.setRawData(
          this.storageKey,
          JSON.stringify(allSettings)
        )
        if (!saveResult.success) {
          throw new Error('Failed to save video settings to storage')
        }
      }

      // 更新缓存 / Update cache
      this.settingsCache.set(settings.fileId, updatedSettings)

      logger.info(`✅ 视频设置保存成功: ${settings.fileId}`)
      return true
    } catch (error) {
      logger.error(`❌ 保存视频设置失败: ${settings.fileId}`, error)
      return false
    }
  }

  /**
   * 更新视频播放设置 / Update video playback settings
   */
  async updatePlaybackSettings(
    fileId: string,
    filePath: string,
    playbackSettings: Partial<VideoPlaybackSettings>
  ): Promise<boolean> {
    try {
      const currentSettings = await this.getVideoSettings(fileId, filePath)
      const updatedSettings: VideoSettings = {
        ...currentSettings,
        playback: { ...currentSettings.playback, ...playbackSettings }
      }

      return await this.saveVideoSettings(updatedSettings)
    } catch (error) {
      logger.error(`❌ 更新播放设置失败: ${fileId}`, error)
      return false
    }
  }

  /**
   * 更新视频UI配置 / Update video UI configuration
   */
  async updateUIConfig(
    fileId: string,
    filePath: string,
    uiConfig: Partial<VideoUIConfig>
  ): Promise<boolean> {
    try {
      const currentSettings = await this.getVideoSettings(fileId, filePath)
      const updatedSettings: VideoSettings = {
        ...currentSettings,
        ui: { ...currentSettings.ui, ...uiConfig }
      }

      return await this.saveVideoSettings(updatedSettings)
    } catch (error) {
      logger.error(`❌ 更新UI配置失败: ${fileId}`, error)
      return false
    }
  }

  /**
   * 更新播放速度 / Update playback speed
   */
  async updatePlaybackRate(
    fileId: string,
    filePath: string,
    playbackRate: number
  ): Promise<boolean> {
    return this.updatePlaybackSettings(fileId, filePath, { playbackRate })
  }

  /**
   * 更新音量 / Update volume
   */
  async updateVolume(fileId: string, filePath: string, volume: number): Promise<boolean> {
    return this.updatePlaybackSettings(fileId, filePath, { volume })
  }

  /**
   * 更新播放位置 / Update play position
   */
  async updatePlayPosition(fileId: string, filePath: string, position: number): Promise<boolean> {
    return this.updateUIConfig(fileId, filePath, { lastPosition: position })
  }

  /**
   * 更新字幕设置 / Update subtitle settings
   */
  async updateSubtitleSettings(
    fileId: string,
    filePath: string,
    subtitleSettings: Partial<SubtitleDisplaySettings>
  ): Promise<boolean> {
    const currentSettings = await this.getVideoSettings(fileId, filePath)
    const updatedSubtitleDisplay = {
      ...currentSettings.playback.subtitleDisplay,
      ...subtitleSettings
    }

    return this.updatePlaybackSettings(fileId, filePath, {
      subtitleDisplay: updatedSubtitleDisplay
    })
  }

  /**
   * 删除视频设置 / Delete video settings
   */
  async deleteVideoSettings(fileId: string): Promise<boolean> {
    try {
      logger.info(`🗑️ 正在删除视频设置: ${fileId}`)

      const allSettingsResult = await this.ipcClient.storage.getRawData(this.storageKey)
      const allSettings =
        allSettingsResult.success && allSettingsResult.data
          ? JSON.parse(allSettingsResult.data)
          : {}
      delete allSettings[fileId]
      const saveResult = await this.ipcClient.storage.setRawData(
        this.storageKey,
        JSON.stringify(allSettings)
      )
      if (!saveResult.success) {
        throw new Error('Failed to delete video settings from storage')
      }

      // 从缓存中移除 / Remove from cache
      this.settingsCache.delete(fileId)

      logger.info(`✅ 视频设置删除成功: ${fileId}`)
      return true
    } catch (error) {
      logger.error(`❌ 删除视频设置失败: ${fileId}`, error)
      return false
    }
  }

  /**
   * 获取所有视频设置 / Get all video settings
   */
  async getAllVideoSettings(): Promise<Record<string, VideoSettings>> {
    try {
      const allSettingsResult = await this.ipcClient.storage.getRawData(this.storageKey)
      return allSettingsResult.success && allSettingsResult.data
        ? JSON.parse(allSettingsResult.data)
        : {}
    } catch (error) {
      logger.error('❌ 获取所有视频设置失败:', error)
      return {}
    }
  }

  /**
   * 清除缓存 / Clear cache
   */
  clearCache(): void {
    this.settingsCache.clear()
    logger.info('🧹 视频设置缓存已清除')
  }

  /**
   * 获取默认视频设置 / Get default video settings
   */
  private getDefaultVideoSettings(fileId: string, filePath: string): VideoSettings {
    return {
      fileId,
      filePath,
      playback: {
        playbackRate: 1.0,
        volume: 0.8,
        autoPlay: false,
        isAutoPause: false,
        displayMode: 'original',
        selectedPlaybackRates: [0.5, 0.75, 1.0, 1.25, 1.5, 2.0],
        loopSettings: {
          isSingleLoop: false,
          intervalMs: 1000,
          loopCount: -1
        },
        subtitleDisplay: {
          enabled: true,
          displayMode: 'original',
          fontSize: 16,
          position: 'bottom',
          backgroundOpacity: 0.8,
          textColor: '#ffffff',
          backgroundColor: '#000000'
        },
        isSubtitleLayoutLocked: false
      },
      ui: {
        showControlBar: true,
        showSubtitleList: true,
        showPlayPageHeader: true,
        lastPosition: 0,
        currentSubtitleIndex: 0,
        windowState: {
          isFullscreen: false
        }
      },
      lastUpdated: Date.now(),
      version: '2.0.0'
    }
  }

  /**
   * 验证视频设置格式 / Validate video settings format
   */
  private isValidVideoSettings(settings: unknown): settings is VideoSettings {
    if (!settings || typeof settings !== 'object') {
      return false
    }

    const s = settings as Record<string, unknown>

    return !!(
      typeof s.fileId === 'string' &&
      typeof s.filePath === 'string' &&
      s.playback &&
      s.ui &&
      typeof s.lastUpdated === 'number' &&
      typeof s.version === 'string'
    )
  }

  /**
   * 迁移旧版本视频设置 / Migrate old version video settings
   */
  private migrateVideoSettings(settings: VideoSettings): VideoSettings {
    // 这里可以添加版本迁移逻辑 / Version migration logic can be added here
    if (settings.version === '1.0.0') {
      logger.info(`📦 执行视频设置迁移: ${settings.fileId} 1.0.0 → 2.0.0`)
    }

    return {
      ...settings,
      version: '2.0.0',
      lastUpdated: Date.now()
    }
  }
}

/**
 * 默认导出服务实例 / Default export service instance
 */
export const videoSettingsService = new VideoSettingsService()

// 类型定义已通过 index.ts 导出 / Type definitions are exported through index.ts

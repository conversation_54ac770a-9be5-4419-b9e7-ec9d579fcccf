/**
 * 存储服务入口文件 / Storage Services Entry Point
 *
 * 包含所有与存储相关的服务接口和实现
 * Contains all storage-related service interfaces and implementations
 */

// 导出最近播放记录服务 / Export recent plays service
export { RecentPlaysService } from './recent-plays.service'

// 导出视频设置服务 / Export video settings service
export { VideoSettingsService, videoSettingsService } from './video-settings.service'

// 导出存储服务类型 / Export storage service types
export type {
  IConfigStorageService,
  ICacheStorageService,
  IUserDataStorageService,
  IStorageService
} from '../../infrastructure/types/service/storage.types'

// 导出新服务的类型 / Export new service types
export type {
  RecentPlayItem,
  RecentPlaysQueryParams,
  RecentPlaysStats
} from './recent-plays.service'

export type {
  VideoSettings,
  VideoPlaybackSettings,
  VideoUIConfig,
  SubtitleDisplaySettings,
  LoopSettings,
  VideoSettingsUpdateOptions
} from './video-settings.service'

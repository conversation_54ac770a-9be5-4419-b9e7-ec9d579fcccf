/**
 * 最近播放记录服务 / Recent Plays Service
 *
 * 管理用户最近观看的视频记录，包括存储、检索、更新和删除操作
 * Manages user's recent video viewing records including storage, retrieval, update and deletion operations
 */

import { logger } from '@renderer/utils/logger'
import { IPCClientService } from '../api'

/**
 * 简化的最近播放项目接口 / Simplified Recent Play Item Interface
 * 这个接口与v2架构的需求保持一致，简化了复杂的域模型
 * This interface aligns with v2 architecture needs, simplifying complex domain models
 */
export interface RecentPlayItem {
  /** 唯一标识符 / Unique identifier */
  id: string

  /** 视频文件路径 / Video file path */
  filePath: string

  /** 视频文件名 / Video file name */
  fileName: string

  /** 视频时长(秒) / Video duration in seconds */
  duration: number

  /** 播放进度(秒) / Playback progress in seconds */
  currentTime: number

  /** 缩略图路径 / Thumbnail path */
  thumbnailPath?: string

  /** 字幕文件路径 / Subtitle file path */
  subtitlePath?: string

  /** 最后播放时间 / Last played timestamp */
  lastPlayedAt: number

  /** 创建时间 / Created timestamp */
  createdAt: number

  /** 文件大小(字节) / File size in bytes */
  fileSize: number

  /** 播放次数 / Play count */
  playCount: number

  /** 是否收藏 / Whether favorited */
  isFavorite: boolean
}

/**
 * 最近播放查询参数 / Recent Plays Query Parameters
 */
export interface RecentPlaysQueryParams {
  /** 限制返回数量 / Limit result count */
  limit?: number

  /** 跳过数量 / Skip count */
  offset?: number

  /** 排序字段 / Sort field */
  sortBy?: 'lastPlayedAt' | 'createdAt' | 'fileName' | 'playCount'

  /** 排序顺序 / Sort order */
  sortOrder?: 'asc' | 'desc'

  /** 搜索关键词 / Search keyword */
  search?: string

  /** 是否只返回收藏 / Whether to return only favorites */
  favoritesOnly?: boolean
}

/**
 * 最近播放统计信息 / Recent Plays Statistics
 */
export interface RecentPlaysStats {
  /** 总记录数 / Total count */
  totalCount: number

  /** 收藏数量 / Favorites count */
  favoritesCount: number

  /** 今日播放数量 / Today's play count */
  todayPlayCount: number

  /** 本周播放数量 / This week's play count */
  weekPlayCount: number

  /** 总播放时长(秒) / Total watch time in seconds */
  totalWatchTime: number
}

/**
 * 最近播放服务类 / Recent Plays Service Class
 */
export class RecentPlaysService {
  private readonly storageKey = 'recent-plays'
  private readonly maxRecentPlays = 100 // 最大保存数量 / Maximum saved count
  private ipcClient: IPCClientService

  constructor() {
    this.ipcClient = new IPCClientService()
  }

  /**
   * 获取最近播放列表 / Get recent plays list
   */
  async getRecentPlays(params: RecentPlaysQueryParams = {}): Promise<RecentPlayItem[]> {
    try {
      logger.info('📋 正在获取最近播放列表...', params)

      // 从存储中获取所有记录 / Get all records from storage
      const allPlays = await this.getAllPlaysFromStorage()

      // 应用过滤和排序 / Apply filtering and sorting
      let filteredPlays = this.applyFilters(allPlays, params)
      filteredPlays = this.applySorting(filteredPlays, params)

      // 应用分页 / Apply pagination
      const { offset = 0, limit = 20 } = params
      const result = filteredPlays.slice(offset, offset + limit)

      logger.info(`✅ 成功获取 ${result.length} 条最近播放记录`)
      return result
    } catch (error) {
      logger.error('❌ 获取最近播放列表失败:', error)
      throw new Error(
        `Failed to get recent plays: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  /**
   * 添加或更新播放记录 / Add or update play record
   */
  async addOrUpdatePlay(
    item: Omit<RecentPlayItem, 'id' | 'createdAt' | 'lastPlayedAt' | 'playCount'>
  ): Promise<RecentPlayItem> {
    try {
      logger.info('📝 正在添加/更新播放记录:', item.fileName)

      const allPlays = await this.getAllPlaysFromStorage()
      const existingIndex = allPlays.findIndex((play) => play.filePath === item.filePath)

      const now = Date.now()
      let updatedItem: RecentPlayItem

      if (existingIndex >= 0) {
        // 更新现有记录 / Update existing record
        const existing = allPlays[existingIndex]
        updatedItem = {
          ...existing,
          ...item,
          lastPlayedAt: now,
          playCount: existing.playCount + 1
        }
        allPlays[existingIndex] = updatedItem
      } else {
        // 创建新记录 / Create new record
        updatedItem = {
          ...item,
          id: this.generateId(),
          createdAt: now,
          lastPlayedAt: now,
          playCount: 1
        }
        allPlays.unshift(updatedItem)
      }

      // 限制最大数量 / Limit maximum count
      if (allPlays.length > this.maxRecentPlays) {
        allPlays.splice(this.maxRecentPlays)
      }

      await this.saveAllPlaysToStorage(allPlays)

      logger.info('✅ 播放记录添加/更新成功')
      return updatedItem
    } catch (error) {
      logger.error('❌ 添加/更新播放记录失败:', error)
      throw new Error(
        `Failed to add/update play record: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  /**
   * 删除播放记录 / Delete play record
   */
  async deletePlay(id: string): Promise<boolean> {
    try {
      logger.info(`🗑️ 正在删除播放记录: ${id}`)

      const allPlays = await this.getAllPlaysFromStorage()
      const filteredPlays = allPlays.filter((play) => play.id !== id)

      if (filteredPlays.length === allPlays.length) {
        logger.warn(`⚠️ 未找到ID为 ${id} 的播放记录`)
        return false
      }

      await this.saveAllPlaysToStorage(filteredPlays)

      logger.info('✅ 播放记录删除成功')
      return true
    } catch (error) {
      logger.error('❌ 删除播放记录失败:', error)
      throw new Error(
        `Failed to delete play record: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  /**
   * 清空所有播放记录 / Clear all play records
   */
  async clearAllPlays(): Promise<void> {
    try {
      logger.info('🧹 正在清空所有播放记录...')

      const result = await this.ipcClient.storage.setRawData(this.storageKey, JSON.stringify([]))
      if (!result.success) {
        throw new Error(result.error || 'Failed to clear plays')
      }

      logger.info('✅ 所有播放记录已清空')
    } catch (error) {
      logger.error('❌ 清空播放记录失败:', error)
      throw new Error(
        `Failed to clear play records: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  /**
   * 切换收藏状态 / Toggle favorite status
   */
  async toggleFavorite(id: string): Promise<boolean> {
    try {
      logger.info(`⭐ 正在切换收藏状态: ${id}`)

      const allPlays = await this.getAllPlaysFromStorage()
      const playIndex = allPlays.findIndex((play) => play.id === id)

      if (playIndex === -1) {
        throw new Error(`Play record with id ${id} not found`)
      }

      allPlays[playIndex].isFavorite = !allPlays[playIndex].isFavorite
      await this.saveAllPlaysToStorage(allPlays)

      const newStatus = allPlays[playIndex].isFavorite
      logger.info(`✅ 收藏状态已切换为: ${newStatus ? '收藏' : '取消收藏'}`)
      return newStatus
    } catch (error) {
      logger.error('❌ 切换收藏状态失败:', error)
      throw new Error(
        `Failed to toggle favorite: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  /**
   * 获取统计信息 / Get statistics
   */
  async getStats(): Promise<RecentPlaysStats> {
    try {
      logger.info('📊 正在获取播放统计信息...')

      const allPlays = await this.getAllPlaysFromStorage()
      const now = Date.now()
      const todayStart = new Date(new Date().setHours(0, 0, 0, 0)).getTime()
      const weekStart = now - 7 * 24 * 60 * 60 * 1000

      const stats: RecentPlaysStats = {
        totalCount: allPlays.length,
        favoritesCount: allPlays.filter((play) => play.isFavorite).length,
        todayPlayCount: allPlays.filter((play) => play.lastPlayedAt >= todayStart).length,
        weekPlayCount: allPlays.filter((play) => play.lastPlayedAt >= weekStart).length,
        totalWatchTime: allPlays.reduce((total, play) => total + play.currentTime, 0)
      }

      logger.info('✅ 统计信息获取成功')
      return stats
    } catch (error) {
      logger.error('❌ 获取统计信息失败:', error)
      throw new Error(
        `Failed to get statistics: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  /**
   * 从存储中获取所有播放记录 / Get all play records from storage
   */
  private async getAllPlaysFromStorage(): Promise<RecentPlayItem[]> {
    try {
      const result = await this.ipcClient.storage.getRawData(this.storageKey)
      if (!result.success) {
        logger.warn('⚠️ 获取存储数据失败:', result.error)
        return []
      }

      if (!result.data) {
        return []
      }

      // 增强的数据验证 / Enhanced data validation
      const data = JSON.parse(result.data)
      if (!Array.isArray(data)) {
        logger.warn('⚠️ 存储数据格式无效，重置为空数组')
        return []
      }

      // 验证并过滤有效的播放记录 / Validate and filter valid play records
      const validPlays = data.filter((item): item is RecentPlayItem => {
        return this.validateRecentPlayItem(item)
      })

      if (validPlays.length !== data.length) {
        logger.warn(`⚠️ 发现 ${data.length - validPlays.length} 个无效的播放记录，已过滤`)
        // 保存清理后的数据 / Save cleaned data
        await this.saveAllPlaysToStorage(validPlays).catch((saveError) => {
          logger.error('❌ 保存清理后的数据失败:', saveError)
        })
      }

      return validPlays
    } catch (error) {
      logger.error('❌ 从存储获取播放记录失败:', error)
      if (error instanceof SyntaxError) {
        logger.error('❌ JSON解析失败，可能数据已损坏，重置存储')
        // 重置损坏的数据 / Reset corrupted data
        await this.saveAllPlaysToStorage([]).catch(() => {
          // 静默处理保存失败 / Silently handle save failure
        })
      }
      return []
    }
  }

  /**
   * 保存所有播放记录到存储 / Save all play records to storage
   */
  private async saveAllPlaysToStorage(plays: RecentPlayItem[]): Promise<void> {
    const result = await this.ipcClient.storage.setRawData(this.storageKey, JSON.stringify(plays))
    if (!result.success) {
      throw new Error(result.error || 'Failed to save plays to storage')
    }
  }

  /**
   * 应用过滤条件 / Apply filters
   */
  private applyFilters(plays: RecentPlayItem[], params: RecentPlaysQueryParams): RecentPlayItem[] {
    let filtered = plays

    // 搜索过滤 / Search filter
    if (params.search) {
      const searchLower = params.search.toLowerCase()
      filtered = filtered.filter(
        (play) =>
          play.fileName.toLowerCase().includes(searchLower) ||
          play.filePath.toLowerCase().includes(searchLower)
      )
    }

    // 收藏过滤 / Favorites filter
    if (params.favoritesOnly) {
      filtered = filtered.filter((play) => play.isFavorite)
    }

    return filtered
  }

  /**
   * 应用排序 / Apply sorting
   */
  private applySorting(plays: RecentPlayItem[], params: RecentPlaysQueryParams): RecentPlayItem[] {
    const { sortBy = 'lastPlayedAt', sortOrder = 'desc' } = params

    return plays.sort((a, b) => {
      let comparison = 0

      switch (sortBy) {
        case 'lastPlayedAt':
          comparison = a.lastPlayedAt - b.lastPlayedAt
          break
        case 'createdAt':
          comparison = a.createdAt - b.createdAt
          break
        case 'fileName':
          comparison = a.fileName.localeCompare(b.fileName)
          break
        case 'playCount':
          comparison = a.playCount - b.playCount
          break
        default:
          comparison = a.lastPlayedAt - b.lastPlayedAt
      }

      return sortOrder === 'desc' ? -comparison : comparison
    })
  }

  /**
   * 验证最近播放项目数据 / Validate recent play item data
   */
  private validateRecentPlayItem(item: unknown): item is RecentPlayItem {
    if (!item || typeof item !== 'object') {
      return false
    }

    const play = item as Partial<RecentPlayItem>

    // 必需字段验证 / Required fields validation
    if (
      !play.id ||
      typeof play.id !== 'string' ||
      !play.filePath ||
      typeof play.filePath !== 'string' ||
      !play.fileName ||
      typeof play.fileName !== 'string'
    ) {
      return false
    }

    // 数值字段验证 / Numeric fields validation
    if (
      typeof play.duration !== 'number' ||
      typeof play.currentTime !== 'number' ||
      typeof play.lastPlayedAt !== 'number' ||
      typeof play.createdAt !== 'number' ||
      typeof play.fileSize !== 'number' ||
      typeof play.playCount !== 'number' ||
      play.duration < 0 ||
      play.currentTime < 0 ||
      play.lastPlayedAt <= 0 ||
      play.createdAt <= 0 ||
      play.fileSize < 0 ||
      play.playCount < 0
    ) {
      return false
    }

    // 布尔字段验证 / Boolean fields validation
    if (typeof play.isFavorite !== 'boolean') {
      return false
    }

    // 可选字段验证 / Optional fields validation
    if (play.thumbnailPath !== undefined && typeof play.thumbnailPath !== 'string') {
      return false
    }

    if (play.subtitlePath !== undefined && typeof play.subtitlePath !== 'string') {
      return false
    }

    return true
  }

  /**
   * 生成唯一ID / Generate unique ID
   */
  private generateId(): string {
    return `play_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

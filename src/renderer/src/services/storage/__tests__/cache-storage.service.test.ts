/**
 * 缓存存储服务测试 / Cache Storage Service Tests
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { CacheStorageService, createCacheStorageService } from '../cache-storage.service'
import { StorageEngine } from '../../../infrastructure/types/service/storage.types'

describe('CacheStorageService', () => {
  let service: CacheStorageService

  beforeEach(async () => {
    service = createCacheStorageService({
      maxSize: 100,
      ttl: 1000, // 1秒TTL用于测试 / 1 second TTL for testing
      engine: StorageEngine.MEMORY
    })
    await service.initialize()
  })

  afterEach(async () => {
    await service.dispose()
  })

  describe('基本操作 / Basic Operations', () => {
    it('应该能够设置和获取值 / Should be able to set and get values', async () => {
      const setResult = await service.set('key1', 'value1')
      expect(setResult.success).toBe(true)

      const getResult = await service.get('key1')
      expect(getResult.success).toBe(true)
      expect(getResult.data).toBe('value1')
    })

    it('应该为不存在的键返回默认值 / Should return default value for non-existent keys', async () => {
      const result = await service.get('nonexistent', 'default')
      expect(result.success).toBe(true)
      expect(result.data).toBe('default')
    })

    it('应该能够检查键是否存在 / Should be able to check if key exists', async () => {
      await service.set('key1', 'value1')

      expect(await service.has('key1')).toBe(true)
      expect(await service.has('nonexistent')).toBe(false)
    })

    it('应该能够删除键 / Should be able to delete keys', async () => {
      await service.set('key1', 'value1')

      const deleteResult = await service.delete('key1')
      expect(deleteResult.success).toBe(true)

      expect(await service.has('key1')).toBe(false)
    })

    it('应该能够清空所有缓存 / Should be able to clear all cache', async () => {
      await service.set('key1', 'value1')
      await service.set('key2', 'value2')

      const clearResult = await service.clear()
      expect(clearResult.success).toBe(true)

      expect(await service.size()).toBe(0)
    })
  })

  describe('TTL功能 / TTL Functionality', () => {
    it('应该在TTL过期后自动删除项 / Should automatically delete items after TTL expires', async () => {
      await service.set('key1', 'value1', 100) // 100ms TTL

      expect(await service.has('key1')).toBe(true)

      // 等待TTL过期 / Wait for TTL to expire
      await new Promise((resolve) => setTimeout(resolve, 150))

      expect(await service.has('key1')).toBe(false)
    })

    it('应该在获取过期项时返回默认值 / Should return default value when getting expired items', async () => {
      await service.set('key1', 'value1', 100) // 100ms TTL

      // 等待TTL过期 / Wait for TTL to expire
      await new Promise((resolve) => setTimeout(resolve, 150))

      const result = await service.get('key1', 'default')
      expect(result.success).toBe(true)
      expect(result.data).toBe('default')
    })
  })

  describe('标签系统 / Tag System', () => {
    it('应该能够使用标签设置值 / Should be able to set values with tags', async () => {
      const result = await service.setWithTags('key1', 'value1', ['tag1', 'tag2'])
      expect(result.success).toBe(true)
    })

    it('应该能够根据标签获取值 / Should be able to get values by tag', async () => {
      await service.setWithTags('key1', 'value1', ['category:user'])
      await service.setWithTags('key2', 'value2', ['category:user'])
      await service.setWithTags('key3', 'value3', ['category:system'])

      const userResult = await service.getByTag<string>('category:user')
      expect(userResult.success).toBe(true)
      expect(userResult.data).toHaveLength(2)
      expect(userResult.data).toContain('value1')
      expect(userResult.data).toContain('value2')
    })

    it('应该能够根据标签删除值 / Should be able to delete values by tag', async () => {
      await service.setWithTags('key1', 'value1', ['category:temp'])
      await service.setWithTags('key2', 'value2', ['category:temp'])
      await service.setWithTags('key3', 'value3', ['category:permanent'])

      const deleteResult = await service.deleteByTag('category:temp')
      expect(deleteResult.success).toBe(true)

      expect(await service.has('key1')).toBe(false)
      expect(await service.has('key2')).toBe(false)
      expect(await service.has('key3')).toBe(true)
    })
  })

  describe('批量操作 / Batch Operations', () => {
    it('应该能够批量设置多个值 / Should be able to set multiple values', async () => {
      const items = {
        key1: 'value1',
        key2: 'value2',
        key3: 'value3'
      }

      const result = await service.setMultiple(items)
      expect(result.success).toBe(true)

      expect(await service.has('key1')).toBe(true)
      expect(await service.has('key2')).toBe(true)
      expect(await service.has('key3')).toBe(true)
    })

    it('应该能够批量获取多个值 / Should be able to get multiple values', async () => {
      await service.set('key1', 'value1')
      await service.set('key2', 'value2')

      const result = await service.getMultiple(['key1', 'key2', 'key3'], 'default')
      expect(result.success).toBe(true)
      expect(result.data).toEqual({
        key1: 'value1',
        key2: 'value2'
      })
    })

    it('应该能够批量删除多个值 / Should be able to delete multiple values', async () => {
      await service.set('key1', 'value1')
      await service.set('key2', 'value2')
      await service.set('key3', 'value3')

      const result = await service.deleteMultiple(['key1', 'key2'])
      expect(result.success).toBe(true)

      expect(await service.has('key1')).toBe(false)
      expect(await service.has('key2')).toBe(false)
      expect(await service.has('key3')).toBe(true)
    })
  })

  describe('查询操作 / Query Operations', () => {
    beforeEach(async () => {
      await service.set('key1', 'value1')
      await service.set('key2', 'value2')
      await service.set('key3', 'value3')
    })

    it('应该能够获取所有键 / Should be able to get all keys', async () => {
      const result = await service.keys()
      expect(result.success).toBe(true)
      expect(result.data).toEqual(['key1', 'key2', 'key3'])
    })

    it('应该能够获取所有值 / Should be able to get all values', async () => {
      const result = await service.values<string>()
      expect(result.success).toBe(true)
      expect(result.data).toEqual(['value1', 'value2', 'value3'])
    })

    it('应该能够获取所有键值对 / Should be able to get all entries', async () => {
      const result = await service.entries<string>()
      expect(result.success).toBe(true)
      expect(result.data).toEqual([
        ['key1', 'value1'],
        ['key2', 'value2'],
        ['key3', 'value3']
      ])
    })
  })

  describe('LRU驱逐策略 / LRU Eviction Strategy', () => {
    it('应该在达到最大容量时驱逐最少使用的项 / Should evict least recently used items when reaching max capacity', async () => {
      // 设置一个小的最大容量进行测试 / Set a small max capacity for testing
      await service.setMaxSize(3)

      // 填满缓存 / Fill the cache
      await service.set('key1', 'value1')
      await service.set('key2', 'value2')
      await service.set('key3', 'value3')

      // 访问key1使其成为最近使用的 / Access key1 to make it most recently used
      await service.get('key1')

      // 添加新项应该驱逐key2（最少使用） / Adding new item should evict key2 (least recently used)
      await service.set('key4', 'value4')

      expect(await service.has('key1')).toBe(true) // 最近访问的 / Recently accessed
      expect(await service.has('key2')).toBe(false) // 应该被驱逐 / Should be evicted
      expect(await service.has('key3')).toBe(true) // 较新的 / More recent
      expect(await service.has('key4')).toBe(true) // 新添加的 / Newly added
    })
  })

  describe('统计信息 / Statistics', () => {
    it('应该正确跟踪缓存命中率 / Should correctly track cache hit rate', async () => {
      await service.set('key1', 'value1')

      // 命中 / Hit
      await service.get('key1')
      await service.get('key1')

      // 未命中 / Miss
      await service.get('nonexistent')

      const hitRate = await service.getHitRate()
      const missRate = await service.getMissRate()

      expect(hitRate).toBeCloseTo(2 / 3) // 2 hits out of 3 accesses
      expect(missRate).toBeCloseTo(1 / 3) // 1 miss out of 3 accesses
    })

    it('应该提供详细的统计信息 / Should provide detailed statistics', async () => {
      await service.set('key1', 'value1')
      await service.set('key2', 'value2')
      await service.get('key1')
      await service.delete('key2')

      const stats = await service.statistics()

      expect(stats.totalItems).toBe(1)
      expect(stats.engineType).toBe(StorageEngine.MEMORY)
      expect(stats.namespace).toBe('cache')
    })
  })

  describe('清理操作 / Cleanup Operations', () => {
    it('应该清理过期的项 / Should cleanup expired items', async () => {
      await service.set('key1', 'value1', 100) // 100ms TTL
      await service.set('key2', 'value2', 1000) // 1s TTL

      // 等待第一个项过期 / Wait for first item to expire
      await new Promise((resolve) => setTimeout(resolve, 150))

      const cleanupResult = await service.cleanup()
      expect(cleanupResult.success).toBe(true)
      expect(cleanupResult.data).toBe(1) // 1 item cleaned

      expect(await service.has('key1')).toBe(false)
      expect(await service.has('key2')).toBe(true)
    })
  })

  describe('备份和恢复 / Backup and Restore', () => {
    it('应该能够备份和恢复缓存数据 / Should be able to backup and restore cache data', async () => {
      await service.set('key1', 'value1')
      await service.set('key2', 'value2')

      const backupResult = await service.backup()
      expect(backupResult.success).toBe(true)
      expect(typeof backupResult.data).toBe('string')

      await service.clear()
      expect(await service.size()).toBe(0)

      const restoreResult = await service.restore(backupResult.data!)
      expect(restoreResult.success).toBe(true)

      expect(await service.has('key1')).toBe(true)
      expect(await service.has('key2')).toBe(true)
    })
  })

  describe('预加载功能 / Preloading Functionality', () => {
    it('应该能够预加载数据 / Should be able to preload data', async () => {
      const loader = vi.fn().mockImplementation(async (key: string) => {
        return `loaded-${key}`
      })

      const keys = ['key1', 'key2', 'key3']
      const result = await service.preload(keys, loader)

      expect(result.success).toBe(true)
      expect(loader).toHaveBeenCalledTimes(3)

      const value1 = await service.get('key1')
      expect(value1.data).toBe('loaded-key1')
    })

    it('应该跳过已存在的键进行预加载 / Should skip existing keys when preloading', async () => {
      await service.set('key1', 'existing-value')

      const loader = vi.fn().mockImplementation(async (key: string) => {
        return `loaded-${key}`
      })

      const keys = ['key1', 'key2']
      await service.preload(keys, loader)

      expect(loader).toHaveBeenCalledTimes(1) // Only called for key2
      expect(loader).toHaveBeenCalledWith('key2')

      const value1 = await service.get('key1')
      expect(value1.data).toBe('existing-value') // 保持原值 / Keep original value
    })
  })

  describe('刷新功能 / Refresh Functionality', () => {
    it('应该能够刷新现有键的值 / Should be able to refresh existing key values', async () => {
      await service.set('key1', 'old-value')

      const loader = vi.fn().mockResolvedValue('new-value')
      const result = await service.refresh('key1', loader)

      expect(result.success).toBe(true)
      expect(loader).toHaveBeenCalledWith('key1')

      const value = await service.get('key1')
      expect(value.data).toBe('new-value')
    })
  })

  describe('驱逐策略配置 / Eviction Policy Configuration', () => {
    it('应该能够设置驱逐策略 / Should be able to set eviction policy', async () => {
      const result = await service.setEvictionPolicy('lfu')
      expect(result.success).toBe(true)
    })

    it('应该能够动态调整最大容量 / Should be able to dynamically adjust max size', async () => {
      await service.set('key1', 'value1')
      await service.set('key2', 'value2')
      await service.set('key3', 'value3')

      expect(await service.size()).toBe(3)

      const result = await service.setMaxSize(2)
      expect(result.success).toBe(true)

      expect(await service.size()).toBe(2) // 应该驱逐一个项 / Should evict one item
    })
  })

  describe('统计重置 / Statistics Reset', () => {
    it('应该能够重置统计信息 / Should be able to reset statistics', async () => {
      await service.set('key1', 'value1')
      await service.get('key1')

      let hitRate = await service.getHitRate()
      expect(hitRate).toBeGreaterThan(0)

      await service.resetStatistics()

      hitRate = await service.getHitRate()
      expect(hitRate).toBe(0)
    })
  })
})

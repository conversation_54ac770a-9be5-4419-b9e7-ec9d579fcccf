/**
 * API 服务入口文件 / API Services Entry Point
 *
 * 包含所有与 API 相关的服务接口和实现
 * Contains all API-related service interfaces and implementations
 */

// IPC 客户端服务 / IPC Client Service
export { IPCClientService, createIPCClientService } from './ipc-client.service'

// 文件选择服务 / File Selection Service
export { FileSelectionService } from './file-selection.service'

// 导出类型定义 / Export type definitions
export type {
  FileSelectionResult,
  FileValidationResult,
  FileSelectionOptions
} from './file-selection.service'

/**
 * 文件选择服务 / File Selection Service
 *
 * 处理视频文件选择、字幕文件关联、文件验证等操作
 * Handles video file selection, subtitle file association, file validation, etc.
 */

import { logger } from '@renderer/utils/logger'
import { IPCClientService } from './ipc-client.service'
import {
  FileDialogProperty,
  SUPPORTED_SUBTITLE_FORMATS,
  SUPPORTED_VIDEO_FORMATS
} from '@renderer/infrastructure'
import { SubtitleFileInfo, SubtitleFormat, VideoFormat } from '@types_/domain'

/**
 * 文件选择结果接口 / File Selection Result Interface
 */
export interface FileSelectionResult {
  /** 是否成功 / Whether successful */
  success: boolean

  /** 选中的视频文件 / Selected video file */
  videoFile?: {
    /** 文件路径 / File path */
    filePath: string
    /** 文件名 / File name */
    fileName: string
    /** 文件大小(字节) / File size in bytes */
    fileSize: number
    /** 文件格式 / File format */
    format: string
    /** 视频时长(秒) / Video duration in seconds */
    duration?: number
    /** 缩略图路径 / Thumbnail path */
    thumbnailPath?: string
  }

  /** 关联的字幕文件 / Associated subtitle files */
  subtitleFiles?: SubtitleFileInfo[]

  /** 错误信息 / Error message */
  error?: string
}

/**
 * 文件验证结果 / File Validation Result
 */
export interface FileValidationResult {
  /** 是否有效 / Whether valid */
  isValid: boolean

  /** 错误信息 / Error messages */
  errors: string[]

  /** 警告信息 / Warning messages */
  warnings: string[]

  /** 文件信息 / File information */
  fileInfo?: {
    size: number
    format: string
    isAccessible: boolean
    lastModified: number
  }
}

/**
 * 文件选择选项 / File Selection Options
 */
export interface FileSelectionOptions {
  /** 是否允许多选 / Whether to allow multiple selection */
  allowMultiple?: boolean

  /** 起始目录 / Starting directory */
  defaultPath?: string

  /** 文件过滤器 / File filters */
  filters?: Array<{
    name: string
    extensions: string[]
  }>

  /** 是否自动查找字幕 / Whether to auto-find subtitles */
  autoFindSubtitles?: boolean

  /** 是否生成缩略图 / Whether to generate thumbnail */
  generateThumbnail?: boolean
}

/**
 * 文件选择服务类 / File Selection Service Class
 */
export class FileSelectionService {
  private ipcClient: IPCClientService

  constructor() {
    this.ipcClient = new IPCClientService()
  }

  /**
   * 选择视频文件 / Select video file
   */
  async selectVideoFile(options: FileSelectionOptions = {}): Promise<FileSelectionResult> {
    try {
      logger.info('🎬 开始选择视频文件...', options)

      // 设置默认选项 / Set default options
      const defaultOptions: FileSelectionOptions = {
        allowMultiple: false,
        autoFindSubtitles: true,
        generateThumbnail: true,
        filters: [
          {
            name: '视频文件',
            extensions: [...SUPPORTED_VIDEO_FORMATS] // VideoFormat枚举值已经是不带点号的扩展名 / VideoFormat enum values are already extensions without dots
          },
          {
            name: '所有文件',
            extensions: ['*']
          }
        ],
        ...options
      }

      // 通过IPC调用主进程的文件选择器 / Call main process file selector via IPC
      const dialogResult = await this.ipcClient.fileSystem.openFileDialog({
        properties: defaultOptions.allowMultiple
          ? [FileDialogProperty.OPEN_FILE, FileDialogProperty.MULTI_SELECTIONS]
          : [FileDialogProperty.OPEN_FILE],
        defaultPath: defaultOptions.defaultPath,
        filters: defaultOptions.filters
      })

      if (!dialogResult.success) {
        throw new Error(dialogResult.error || 'Failed to open file dialog')
      }

      const result = dialogResult.data!

      if (result.canceled || !result.filePaths || result.filePaths.length === 0) {
        logger.info('📋 用户取消了文件选择')
        return { success: false, error: '用户取消了文件选择' }
      }

      const selectedFilePath = result.filePaths[0]
      logger.info(`📁 选中文件: ${selectedFilePath}`)

      // 验证文件 / Validate file
      const validationResult = await this.validateVideoFile(selectedFilePath)
      if (!validationResult.isValid) {
        logger.error('❌ 文件验证失败:', validationResult.errors)
        return {
          success: false,
          error: `文件验证失败: ${validationResult.errors.join(', ')}`
        }
      }

      // 获取文件信息 / Get file information
      const fileInfo = await this.getVideoFileInfo(selectedFilePath)
      if (!fileInfo) {
        return {
          success: false,
          error: '无法获取文件信息'
        }
      }

      // 构建结果对象 / Build result object
      const selectionResult: FileSelectionResult = {
        success: true,
        videoFile: fileInfo
      }

      // 自动查找字幕文件 / Auto-find subtitle files
      if (defaultOptions.autoFindSubtitles) {
        const subtitleFiles = await this.findAssociatedSubtitles(selectedFilePath)
        if (subtitleFiles.length > 0) {
          selectionResult.subtitleFiles = subtitleFiles
          logger.info(`📝 找到 ${subtitleFiles.length} 个字幕文件`)
        }
      }

      // 生成缩略图 / Generate thumbnail
      if (defaultOptions.generateThumbnail && selectionResult.videoFile) {
        try {
          const thumbnailPath = await this.generateVideoThumbnail(selectedFilePath)
          if (thumbnailPath) {
            selectionResult.videoFile.thumbnailPath = thumbnailPath
            logger.info(`🖼️ 缩略图生成成功: ${thumbnailPath}`)
          }
        } catch (error) {
          logger.warn('⚠️ 缩略图生成失败:', error)
          // 不影响主要功能，继续处理 / Don't affect main functionality, continue processing
        }
      }

      logger.info('✅ 视频文件选择成功')
      return selectionResult
    } catch (error) {
      logger.error('❌ 视频文件选择失败:', error)
      return {
        success: false,
        error: `文件选择失败: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  /**
   * 选择字幕文件 / Select subtitle file
   */
  async selectSubtitleFile(videoFilePath?: string): Promise<FileSelectionResult> {
    try {
      logger.info('📝 开始选择字幕文件...')

      // 设置字幕文件过滤器 / Set subtitle file filters
      const filters = [
        {
          name: '字幕文件',
          extensions: SUPPORTED_SUBTITLE_FORMATS.map((ext) => ext.slice(1))
        },
        {
          name: '所有文件',
          extensions: ['*']
        }
      ]

      // 获取默认路径 / Get default path
      let defaultPath: string | undefined
      if (videoFilePath) {
        const pathParts = videoFilePath.split(/[/\\]/)
        pathParts.pop() // 移除文件名，保留目录 / Remove filename, keep directory
        defaultPath = pathParts.join('/')
      }

      const dialogResult = await this.ipcClient.fileSystem.openFileDialog({
        properties: [FileDialogProperty.OPEN_FILE],
        defaultPath,
        filters
      })

      if (!dialogResult.success) {
        throw new Error(dialogResult.error || 'Failed to open subtitle file dialog')
      }

      const result = dialogResult.data!

      if (result.canceled || !result.filePaths || result.filePaths.length === 0) {
        return { success: false, error: '用户取消了字幕文件选择' }
      }

      const selectedFilePath = result.filePaths[0]
      logger.info(`📝 选中字幕文件: ${selectedFilePath}`)

      // 验证字幕文件 / Validate subtitle file
      const validationResult = await this.validateSubtitleFile(selectedFilePath)
      if (!validationResult.isValid) {
        return {
          success: false,
          error: `字幕文件验证失败: ${validationResult.errors.join(', ')}`
        }
      }

      // 获取字幕文件信息 / Get subtitle file information
      const subtitleInfo = await this.getSubtitleFileInfo(selectedFilePath)
      if (!subtitleInfo) {
        return {
          success: false,
          error: '无法获取字幕文件信息'
        }
      }

      logger.info('✅ 字幕文件选择成功')
      return {
        success: true,
        subtitleFiles: [subtitleInfo]
      }
    } catch (error) {
      logger.error('❌ 字幕文件选择失败:', error)
      return {
        success: false,
        error: `字幕文件选择失败: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  /**
   * 验证视频文件 / Validate video file
   */
  async validateVideoFile(filePath: string): Promise<FileValidationResult> {
    try {
      const result: FileValidationResult = {
        isValid: true,
        errors: [],
        warnings: []
      }

      // 检查文件扩展名 / Check file extension
      const extension = this.getFileExtension(filePath)
      if (!extension) {
        result.errors.push('文件缺少扩展名')
        result.isValid = false
      } else if (!this.isSupportedVideoFormat(extension)) {
        result.errors.push(`不支持的视频格式: ${extension}`)
        result.isValid = false
      }

      // 检查文件是否存在且可访问 / Check if file exists and is accessible
      try {
        const fileStatsResult = await this.ipcClient.fileSystem.getFileInfo(filePath)
        if (!fileStatsResult.success) {
          throw new Error(fileStatsResult.error || 'Failed to get file stats')
        }
        const fileStats = fileStatsResult.data
        if (!fileStats) {
          result.errors.push('文件不存在或无法访问')
          result.isValid = false
        } else {
          result.fileInfo = {
            size: fileStats.size,
            format: extension,
            isAccessible: true,
            lastModified: fileStats.mtime
          }

          // 检查文件大小 / Check file size
          if (fileStats.size === 0) {
            result.errors.push('文件大小为0')
            result.isValid = false
          } else if (fileStats.size > 10 * 1024 * 1024 * 1024) {
            // 10GB
            result.warnings.push('文件很大，可能影响性能')
          }
        }
      } catch (error) {
        result.errors.push('无法获取文件信息', error as string)
        result.isValid = false
      }

      return result
    } catch (error) {
      logger.error('❌ 文件验证出错:', error)
      return {
        isValid: false,
        errors: ['文件验证过程出错'],
        warnings: []
      }
    }
  }

  /**
   * 验证字幕文件 / Validate subtitle file
   */
  async validateSubtitleFile(filePath: string): Promise<FileValidationResult> {
    try {
      const result: FileValidationResult = {
        isValid: true,
        errors: [],
        warnings: []
      }

      // 检查文件扩展名 / Check file extension
      const extension = this.getFileExtension(filePath)
      const format = this.extensionToSubtitleFormat(extension)
      if (!format) {
        result.errors.push('文件缺少扩展名')
        result.isValid = false
      } else if (!SUPPORTED_SUBTITLE_FORMATS.includes(format)) {
        result.errors.push(`不支持的字幕格式: ${extension}`)
        result.isValid = false
      }

      // 检查文件是否存在 / Check if file exists
      try {
        const fileStatsResult = await this.ipcClient.fileSystem.getFileInfo(filePath)
        if (!fileStatsResult.success) {
          throw new Error(fileStatsResult.error || 'Failed to get subtitle file stats')
        }
        const fileStats = fileStatsResult.data
        if (!fileStats) {
          result.errors.push('字幕文件不存在或无法访问')
          result.isValid = false
        } else {
          result.fileInfo = {
            size: fileStats.size,
            format: extension,
            isAccessible: true,
            lastModified: fileStats.mtime
          }

          if (fileStats.size === 0) {
            result.errors.push('字幕文件大小为0')
            result.isValid = false
          }
        }
      } catch (error) {
        result.errors.push('无法获取字幕文件信息', error as string)
        result.isValid = false
      }

      return result
    } catch (error) {
      logger.error('❌ 字幕文件验证出错:', error)
      return {
        isValid: false,
        errors: ['字幕文件验证过程出错'],
        warnings: []
      }
    }
  }

  /**
   * 获取视频文件信息 / Get video file information
   */
  private async getVideoFileInfo(
    filePath: string
  ): Promise<FileSelectionResult['videoFile'] | null> {
    try {
      const statsResult = await this.ipcClient.fileSystem.getFileInfo(filePath)
      if (!statsResult.success) {
        return null
      }
      const stats = statsResult.data
      if (!stats) return null

      const fileName = filePath.split(/[/\\]/).pop() || ''
      const format = this.getFileExtension(filePath)

      // 尝试获取视频元数据(时长等) / Try to get video metadata (duration, etc.)
      let duration: number | undefined
      try {
        const metadataResult = await this.ipcClient.ffmpeg.getVideoInfo(filePath)
        const metadata = metadataResult.success ? metadataResult.data : null
        duration = metadata?.duration
      } catch (error) {
        logger.warn('⚠️ 无法获取视频元数据:', error)
      }

      return {
        filePath,
        fileName,
        fileSize: stats.size,
        format,
        duration
      }
    } catch (error) {
      logger.error('❌ 获取视频文件信息失败:', error)
      return null
    }
  }

  /**
   * 获取字幕文件信息 / Get subtitle file information
   */
  private async getSubtitleFileInfo(filePath: string): Promise<SubtitleFileInfo | null> {
    // TODO: 关于字幕文件的元数据，应该直接从持久化文件中读取出来，而不是在这里重复解析
    try {
      const fileName = filePath.split(/[/\\]/).pop() || ''
      const extension = this.getFileExtension(filePath)
      const format = this.extensionToSubtitleFormat(extension)

      if (!format) {
        logger.warn('⚠️ 无法获取字幕文件格式:', extension)
        return null
      }

      // TODO: 尝试从文件名推断语言 / Try to infer language from filename
      // let language: string | undefined
      // const languageMatch = fileName.match(/\.(zh|en|ja|ko|fr|de|es|ru)\./)
      // if (languageMatch) {
      //   language = languageMatch[1]
      // }

      // TODO: 解析

      return {
        filePath,
        fileName: fileName.replace(/\.[^.]+$/, ''), // 移除扩展名 / Remove extension
        format: format as SubtitleFormat,
        encoding: 'utf-8'
      }
    } catch (error) {
      logger.error('❌ 获取字幕文件信息失败:', error)
      return null
    }
  }

  /**
   * 查找关联的字幕文件 / Find associated subtitle files
   */
  private async findAssociatedSubtitles(videoFilePath: string): Promise<SubtitleFileInfo[]> {
    try {
      const pathParts = videoFilePath.split(/[/\\]/)
      const fileName = pathParts.pop() || ''
      const directory = pathParts.join('/')
      const baseName = fileName.replace(/\.[^.]+$/, '') // 移除扩展名 / Remove extension

      // 获取同目录下的所有文件 / Get all files in the same directory
      const readDirResult = await this.ipcClient.fileSystem.readDirectory(directory)
      if (!readDirResult.success || !readDirResult.data) {
        logger.warn(`⚠️ 无法读取目录内容: ${directory}, 错误信息: ${readDirResult.error}`)
        return []
      }

      const files = readDirResult.data
      const subtitleFiles: SubtitleFileInfo[] = []

      for (const file of files) {
        const fileExtension = this.getFileExtension(file)
        const subtitleFormat = this.extensionToSubtitleFormat(fileExtension)
        if (!subtitleFormat) {
          continue // 跳过非字幕文件 / Skip non-subtitle files
        }

        // 检查是否为支持的字幕格式 / Check if it's a supported subtitle format
        if (SUPPORTED_SUBTITLE_FORMATS.includes(subtitleFormat)) {
          // 检查文件名是否匹配 / Check if filename matches
          if (file.startsWith(baseName)) {
            const subtitleInfo = await this.getSubtitleFileInfo(`${directory}/${file}`)
            if (subtitleInfo) {
              subtitleFiles.push(subtitleInfo)
            }
          }
        }
      }

      return subtitleFiles
    } catch (error) {
      logger.error('❌ 查找关联字幕文件失败:', error)
      return []
    }
  }

  /**
   * 生成视频缩略图 / Generate video thumbnail
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private async generateVideoThumbnail(videoFilePath: string): Promise<string | null> {
    try {
      // 通过IPC调用主进程生成缩略图 / Call main process to generate thumbnail via IPC
      // TODO: Implement thumbnail generation functionality
      const thumbnailPath: string | null = null

      return thumbnailPath || null
    } catch (error) {
      logger.error('❌ 生成缩略图失败:', error)
      return null
    }
  }

  /**
   * 获取文件扩展名 / Get file extension
   */
  private getFileExtension(filePath: string): string {
    const match = filePath.match(/\.([^.]+)$/)

    if (!match) {
      return '' // 无扩展名时返回空字符串 / Return empty string when no extension
    }

    return `.${match[1].toLowerCase()}` // 返回带点号的扩展名 / Return extension with dot
  }

  private extensionToSubtitleFormat(extension: string): SubtitleFormat | null {
    // 移除点号(如果存在) / Remove dot if present
    const cleanExt = extension.startsWith('.') ? extension.slice(1) : extension

    // 转换为小写 / Convert to lowercase
    const lowerExt = cleanExt.toLowerCase()

    // 查找匹配的SubtitleFormat / Find matching SubtitleFormat
    return Object.values(SubtitleFormat).find((format) => format === lowerExt) || null
  }

  /**
   * 将文件扩展名转换为VideoFormat枚举 / Convert file extension to VideoFormat enum
   */
  private extensionToVideoFormat(extension: string): VideoFormat | null {
    // 移除点号(如果存在) / Remove dot if present
    const cleanExt = extension.startsWith('.') ? extension.slice(1) : extension

    // 转换为小写 / Convert to lowercase
    const lowerExt = cleanExt.toLowerCase()

    // 查找匹配的VideoFormat / Find matching VideoFormat
    return Object.values(VideoFormat).find((format) => format === lowerExt) || null
  }

  /**
   * 检查是否为支持的视频格式 / Check if it's a supported video format
   */
  private isSupportedVideoFormat(extension: string): boolean {
    const format = this.extensionToVideoFormat(extension)
    if (!format) {
      logger.warn('⚠️ 无法获取视频文件格式:', extension)
      return false
    }
    return (SUPPORTED_VIDEO_FORMATS as readonly VideoFormat[]).includes(format)
  }
}

import React from 'react'
import { Badge } from 'antd'

/**
 * 通知红点组件属性接口 / Notification Badge Component Props Interface
 */
export interface NotificationBadgeProps {
  /**
   * 是否显示红点通知 / Whether to show the red dot notification
   */
  readonly showDot: boolean
  /**
   * 子组件 / Child component to wrap with the badge
   */
  readonly children: React.ReactNode
  /**
   * 自定义样式 / Custom styles
   */
  readonly style?: React.CSSProperties
  /**
   * Badge的偏移量 / Badge offset
   */
  readonly offset?: [number, number]
}

/**
 * 通知红点组件 / Notification Badge Component
 *
 * 基于 Ant Design 的 Badge 组件实现的可复用红点通知组件。
 * 用于在 UI 元素上显示红点提示，适用于各种通知场景。
 *
 * A reusable red dot notification component based on Ant Design's Badge component.
 * Used to display red dot hints on UI elements for various notification scenarios.
 *
 * Features:
 * - 使用 Ant Design Badge 组件，确保主题一致性 / Uses Ant Design Badge for theme consistency
 * - 支持自定义偏移量和样式 / Supports custom offset and styles
 * - 自动适配明暗主题 / Automatically adapts to light/dark themes
 * - 轻量级且易于集成 / Lightweight and easy to integrate
 * - 符合 v2 架构设计原则 / Adheres to v2 architecture design principles
 *
 * @param showDot - 控制红点显示的布尔值 / Boolean to control red dot visibility
 * @param children - 要包装的子组件 / Child component to wrap
 * @param style - 自定义样式 / Custom styles
 * @param offset - Badge偏移量 / Badge offset
 * @returns React.JSX.Element
 */
export function NotificationBadge({
  showDot,
  children,
  style,
  offset
}: NotificationBadgeProps): React.JSX.Element {
  return (
    <Badge
      dot={showDot}
      offset={offset}
      style={{
        ...style,
        // 确保红点可见的样式配置 / Style configuration to ensure red dot visibility
        ...(showDot &&
          ({
            '--ant-badge-dot-size': '8px',
            '--ant-badge-color': '#ff4d4f'
          } as React.CSSProperties))
      }}
    >
      {children}
    </Badge>
  )
}

export default NotificationBadge

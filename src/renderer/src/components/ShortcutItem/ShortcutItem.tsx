import React, { useState, useEffect, useRef } from 'react'
import { KeyboardShortcut } from '../KeyboardShortcut/KeyboardShortcut'
import { shortcutItemStyles } from './ShortcutItem.styles'
import { useTheme } from '@renderer/contexts'

interface ShortcutItemProps {
  config: {
    key: string
    name: string
    description: string
  }
  currentKey: string
  isEditing: boolean
  onEdit: () => void
  onSave: (newKey: string) => void
  onCancel: () => void
  checkConflict: (newKey: string, currentKey: string) => string | null
}

// 检测操作系统平台
const isMac = window.navigator.platform.toLowerCase().includes('mac')

// 禁用的特殊按键
const FORBIDDEN_KEYS = new Set([
  'Enter',
  'Escape',
  'Tab',
  'Backspace',
  'Delete',
  'Insert',
  'Home',
  'End',
  'PageUp',
  'PageDown',
  'CapsLock',
  'NumLock',
  'ScrollLock',
  'PrintScreen',
  'Pause',
  'ContextMenu',
  'F1',
  'F2',
  'F3',
  'F4',
  'F5',
  'F6',
  'F7',
  'F8',
  'F9',
  'F10',
  'F11',
  'F12'
])

/**
 * 单个快捷键配置项组件 - 适配 v2 架构
 * 基于 v1 的 ShortcutItem 组件，使用 v2 主题系统
 */
export function ShortcutItem({
  config,
  currentKey,
  isEditing,
  onEdit,
  onSave,
  onCancel,
  checkConflict
}: ShortcutItemProps): React.JSX.Element {
  const [newKey, setNewKey] = useState('')
  const [isWaiting, setIsWaiting] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')
  const [isHovered, setIsHovered] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [screenWidth, setScreenWidth] = useState(window.innerWidth)
  const inputRef = useRef<HTMLInputElement>(null)
  const { token } = useTheme()

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = (): void => {
      setScreenWidth(window.innerWidth)
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // 当开始编辑时设置等待状态
  useEffect(() => {
    if (isEditing) {
      setIsWaiting(true)
      setNewKey('')
      setErrorMessage('')
      setModalVisible(true)
    } else {
      setIsWaiting(false)
      setModalVisible(false)
    }
  }, [isEditing])

  // 当模态框显示时，确保输入框获得焦点
  useEffect(() => {
    if (!modalVisible) return

    const focusInput = (): void => {
      if (inputRef.current) {
        inputRef.current.focus()
      }
    }

    const timer = setTimeout(focusInput, 200)
    return () => {
      clearTimeout(timer)
    }
  }, [modalVisible])

  const handleKeyDown = (e: React.KeyboardEvent): void => {
    e.preventDefault()
    e.stopPropagation()

    const key = e.key

    // 处理 Enter 确认
    if (key === 'Enter') {
      if (newKey && !errorMessage) {
        handleSave()
      }
      return
    }

    // 处理 Esc 取消
    if (key === 'Escape') {
      handleCancel()
      return
    }

    // 检查是否为禁用的按键
    if (FORBIDDEN_KEYS.has(key)) {
      setErrorMessage(`"${key}" 是系统保留按键，无法设置为快捷键`)
      setNewKey('')
      return
    }

    // 构建快捷键字符串
    let keyString = ''

    // 根据平台使用不同的修饰键
    if (isMac) {
      if (e.metaKey) keyString += 'Cmd+'
      if (e.ctrlKey) keyString += 'Ctrl+'
    } else {
      if (e.ctrlKey) keyString += 'Ctrl+'
    }

    if (e.altKey) keyString += 'Alt+'
    if (e.shiftKey) keyString += 'Shift+'

    // 处理特殊按键
    if (key === ' ') {
      keyString += 'Space'
    } else if (key === '←') {
      keyString += 'ArrowLeft'
    } else if (key === '→') {
      keyString += 'ArrowRight'
    } else if (key === '↑') {
      keyString += 'ArrowUp'
    } else if (key === '↓') {
      keyString += 'ArrowDown'
    } else if (key.length === 1) {
      keyString += key.toUpperCase()
    } else {
      keyString += key
    }

    // 检查快捷键冲突
    const conflictName = checkConflict(keyString, config.key)
    if (conflictName) {
      setErrorMessage(`与 "${conflictName}" 冲突`)
      setNewKey('')
      return
    }

    setNewKey(keyString)
    setErrorMessage('')
    setIsWaiting(false)
  }

  const handleSave = (): void => {
    if (newKey && !errorMessage) {
      onSave(newKey)
      setNewKey('')
      setErrorMessage('')
      setIsWaiting(false)
    }
  }

  const handleCancel = (): void => {
    setNewKey('')
    setErrorMessage('')
    setIsWaiting(false)
    onCancel()
  }

  const styles = shortcutItemStyles(token)

  // 模态框样式
  const modalStyle: React.CSSProperties = {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000
  }

  return (
    <>
      <div
        style={styles.container(isHovered)}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div style={{ flex: 1 }}>
          <div style={{ marginBottom: token.marginXXS }}>
            <span
              style={{
                color: token.colorText,
                fontSize: token.fontSizeLG,
                fontWeight: 600
              }}
            >
              {config.name}
            </span>
          </div>
          <div
            style={{
              color: token.colorTextDescription,
              fontSize: token.fontSizeSM
            }}
          >
            {config.description}
          </div>
        </div>

        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: token.marginSM
          }}
        >
          <KeyboardShortcut shortcut={currentKey} />
          <button
            onClick={onEdit}
            aria-label={`编辑快捷键 ${config.name}`}
            aria-describedby={`shortcut-description-${config.key}`}
            style={{
              padding: `${token.paddingXS}px ${token.paddingSM}px`,
              backgroundColor: 'transparent',
              border: 'none',
              cursor: 'pointer',
              color: token.colorTextDescription,
              fontSize: token.fontSizeSM,
              borderRadius: token.borderRadiusSM,
              transition: `all ${token.motionDurationSlow}`
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = token.colorFillQuaternary
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent'
            }}
          >
            编辑
          </button>
        </div>
      </div>

      {/* 快捷键设置模态框 */}
      {modalVisible && (
        <div style={modalStyle} onClick={handleCancel}>
          <div
            style={styles.modalContentResponsive(screenWidth)}
            onClick={(e) => e.stopPropagation()}
          >
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: token.marginSM,
                marginBottom: token.marginLG
              }}
            >
              <span style={{ fontSize: token.fontSizeLG }}>⌨️</span>
              <span
                style={{
                  fontSize: token.fontSizeLG,
                  fontWeight: 600,
                  color: token.colorText
                }}
              >
                设置快捷键: {config.name}
              </span>
            </div>

            <div
              style={{
                marginBottom: token.marginMD,
                textAlign: 'center',
                color: token.colorTextSecondary
              }}
            >
              {config.description}
            </div>

            <div
              style={{
                ...styles.inputContainer(!!errorMessage),
                width: '100%',
                height: '80px',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                position: 'relative',
                transition: `all ${token.motionDurationMid}`
              }}
            >
              {isWaiting ? (
                <div style={{ textAlign: 'center' }}>
                  <div
                    style={{
                      fontSize: '24px',
                      color: token.colorPrimary,
                      marginBottom: token.marginSM
                    }}
                  >
                    ⌨️
                  </div>
                  <div
                    style={{
                      color: token.colorPrimary,
                      fontWeight: 600
                    }}
                  >
                    请按下快捷键组合...
                  </div>
                </div>
              ) : (
                <KeyboardShortcut
                  shortcut={newKey}
                  style={{
                    fontSize: token.fontSizeLG,
                    fontWeight: 600
                  }}
                />
              )}

              <input
                ref={inputRef}
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  opacity: 0,
                  border: 'none',
                  outline: 'none'
                }}
                onKeyDown={handleKeyDown}
                autoFocus
                readOnly
              />
            </div>

            {errorMessage && (
              <div
                style={{
                  color: token.colorError,
                  marginBottom: token.marginMD,
                  textAlign: 'center',
                  fontSize: token.fontSizeSM
                }}
              >
                {errorMessage}
              </div>
            )}

            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                gap: token.marginMD
              }}
            >
              <button
                onClick={handleCancel}
                style={{
                  padding: `${token.paddingSM}px ${token.paddingMD}px`,
                  backgroundColor: 'transparent',
                  border: `1px solid ${token.colorBorder}`,
                  borderRadius: token.borderRadius,
                  cursor: 'pointer',
                  color: token.colorText,
                  fontSize: token.fontSizeSM
                }}
              >
                取消 (Esc)
              </button>
              <button
                onClick={handleSave}
                disabled={!newKey || !!errorMessage}
                style={styles.primaryButton(!newKey || !!errorMessage)}
                aria-label={`确认快捷键修改 ${config.name}`}
                onMouseEnter={(e) => {
                  if (!(!newKey || !!errorMessage)) {
                    e.currentTarget.style.backgroundColor = token.colorPrimaryHover
                    e.currentTarget.style.transform = 'translateY(-1px)'
                  }
                }}
                onMouseLeave={(e) => {
                  if (!(!newKey || !!errorMessage)) {
                    e.currentTarget.style.backgroundColor = token.colorPrimary
                    e.currentTarget.style.transform = 'translateY(0)'
                  }
                }}
                onMouseDown={(e) => {
                  if (!(!newKey || !!errorMessage)) {
                    e.currentTarget.style.transform = 'translateY(1px)'
                  }
                }}
                onMouseUp={(e) => {
                  if (!(!newKey || !!errorMessage)) {
                    e.currentTarget.style.transform = 'translateY(-1px)'
                  }
                }}
                onFocus={(e) => {
                  e.currentTarget.style.outline = `2px solid ${token.colorPrimaryBorder}`
                  e.currentTarget.style.outlineOffset = '2px'
                }}
                onBlur={(e) => {
                  e.currentTarget.style.outline = 'none'
                  e.currentTarget.style.outlineOffset = '0'
                }}
              >
                确认 (Enter)
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

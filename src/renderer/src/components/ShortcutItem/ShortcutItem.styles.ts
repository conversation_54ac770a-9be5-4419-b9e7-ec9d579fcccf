import { CSSProperties } from 'react'
import type { GlobalToken } from 'antd'

export const shortcutItemStyles = (
  token: GlobalToken
): {
  container: (isHovered: boolean) => CSSProperties
  modalContent: () => CSSProperties
  modalContentResponsive: (screenWidth: number) => CSSProperties
  primaryButton: (disabled: boolean) => CSSProperties
  inputContainer: (hasError: boolean) => CSSProperties
} => ({
  container: (isHovered: boolean): CSSProperties => ({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: `${token.paddingMD}px ${token.paddingLG}px`,
    backgroundColor: isHovered ? token.colorFillQuaternary : 'transparent',
    cursor: 'pointer',
    transition: `all ${token.motionDurationSlow}`,
    borderRadius: token.borderRadiusSM,
    position: 'relative'
  }),

  modalContent: (): CSSProperties => ({
    backgroundColor: token.colorBgContainer,
    borderRadius: token.borderRadiusLG,
    padding: `${token.paddingLG}px`,
    minWidth: 'min(400px, 90vw)',
    maxWidth: '500px',
    boxShadow: token.boxShadowSecondary
  }),

  // 响应式模态框内容样式
  modalContentResponsive: (screenWidth: number): CSSProperties => ({
    backgroundColor: token.colorBgContainer,
    borderRadius: token.borderRadiusLG,
    padding:
      screenWidth <= 480
        ? `${token.paddingSM}px`
        : screenWidth <= 768
          ? `${token.paddingMD}px`
          : `${token.paddingLG}px`,
    minWidth: screenWidth <= 480 ? '95vw' : screenWidth <= 768 ? '85vw' : 'min(400px, 90vw)',
    maxWidth: '500px',
    boxShadow: token.boxShadowSecondary
  }),

  primaryButton: (disabled: boolean): CSSProperties => ({
    padding: `${token.paddingSM}px ${token.paddingMD}px`,
    backgroundColor: disabled ? token.colorBgContainerDisabled : token.colorPrimary,
    border: 'none',
    borderRadius: token.borderRadius,
    cursor: disabled ? 'not-allowed' : 'pointer',
    color: disabled ? token.colorTextDisabled : token.colorWhite,
    fontSize: token.fontSizeSM,
    opacity: disabled ? 0.6 : 1,
    transition: `all ${token.motionDurationMid}`
  }),

  inputContainer: (hasError: boolean): CSSProperties => ({
    backgroundColor: token.colorBgContainer,
    border: `2px solid ${hasError ? token.colorError : token.colorBorder}`,
    borderRadius: token.borderRadiusLG,
    marginBottom: token.marginLG,
    boxShadow: hasError ? `0 0 0 2px ${token.colorErrorBg}` : `0 0 0 2px ${token.colorPrimaryBg}`
  })
})

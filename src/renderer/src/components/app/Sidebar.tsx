import React, { FC } from 'react'
import { Tooltip } from 'antd'
import { HomeOutlined, HeartOutlined } from '@ant-design/icons'
import { useNavigate, useLocation } from 'react-router-dom'
import { useTheme } from '@renderer/contexts'
import { isMac, ThemeMode } from '@renderer/infrastructure'
import styled from 'styled-components'
import useNavBackgroundColor from '@renderer/infrastructure/hooks/useNavBackgroundColor'
import { useFullscreen } from '@renderer/infrastructure/hooks/useFullscreen'
import { Moon, Settings, Sun } from 'lucide-react'

const Sidebar: FC = () => {
  const { token, theme, setTheme } = useTheme()
  const backgroundColor = useNavBackgroundColor()
  const isFullscreen = useFullscreen()
  const { pathname } = useLocation()
  const navigate = useNavigate()

  const to = async (path: string) => {
    navigate(path)
  }

  return (
    <Container
      $isFullscreen={isFullscreen}
      id="app-sidebar"
      style={{ backgroundColor, zIndex: token.zIndexPopupBase }}
    >
      <MainMenusContainer>
        <MainMenus />
      </MainMenusContainer>
      <Divider />
      <Menus>
        <Tooltip title="主题" mouseEnterDelay={0.8} placement="right">
          <Icon
            theme={theme}
            onClick={() => setTheme(theme === ThemeMode.dark ? ThemeMode.light : ThemeMode.dark)}
          >
            {theme === ThemeMode.dark ? (
              <Moon size={20} className="icon" />
            ) : (
              <Sun size={20} className="icon" />
            )}
          </Icon>
        </Tooltip>
        <Tooltip title="设置" mouseEnterDelay={0.8} placement="right">
          <StyledLink
            onClick={async () => {
              await to('/settings/appearance')
            }}
          >
            <Icon theme={theme} className={pathname.startsWith('/settings') ? 'active' : ''}>
              <Settings size={20} className="icon" />
            </Icon>
          </StyledLink>
        </Tooltip>
      </Menus>
    </Container>
  )
}

const MainMenus: FC = () => {
  const { pathname } = useLocation()
  const navigate = useNavigate()

  const isRoute = (path: string): string => (pathname.startsWith(path) ? 'active' : '')

  const pathMap = {
    home: '/home',
    favorites: '/favorites'
  }

  const iconMap = {
    home: <HomeOutlined />,
    favorites: <HeartOutlined />
  }

  return (
    <>
      {Object.keys(pathMap).map((key) => {
        const isDisabled = key === 'favorites'
        const isActive = isRoute(pathMap[key]) === 'active'
        const tooltipTitle = isDisabled ? '' : key

        return (
          <Tooltip key={key} title={tooltipTitle} placement="right" mouseEnterDelay={0.8}>
            <StyledLink
              onClick={isDisabled ? undefined : () => navigate(pathMap[key])}
              className={isActive ? 'active' : ''}
            >
              <Icon className={isActive ? 'active' : ''}>{iconMap[key]}</Icon>
            </StyledLink>
          </Tooltip>
        )
      })}
    </>
  )
}

const Container = styled.div<{ $isFullscreen: boolean }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 0;
  padding-bottom: 12px;
  width: var(--sidebar-width);
  min-width: var(--sidebar-width);
  height: ${({ $isFullscreen }) =>
    isMac && !$isFullscreen ? 'calc(100vh - var(--navbar-height))' : '100vh'};
  -webkit-app-region: drag !important;
  margin-top: ${({ $isFullscreen }) => (isMac && !$isFullscreen ? 'var(--navbar-height)' : 0)};

  .sidebar-avatar {
    margin-bottom: ${isMac ? '12px' : '12px'};
    margin-top: ${isMac ? '0px' : '2px'};
    -webkit-app-region: none;
  }
`

const MainMenusContainer = styled.div`
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  gap: 5px;
`

const Menus = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
`

const Icon = styled.div<{ theme: string }>`
  width: 35px;
  height: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  box-sizing: border-box;
  -webkit-app-region: none;
  border: 0.5px solid transparent;
  &:hover {
    background-color: ${({ theme }) =>
      theme === 'dark' ? 'var(--color-black)' : 'var(--color-white)'};
    opacity: 0.8;
    cursor: pointer;
    .icon {
      color: var(--color-icon-white);
    }
  }
  &.active {
    background-color: ${({ theme }) =>
      theme === 'dark' ? 'var(--color-black)' : 'var(--color-white)'};
    border: 0.5px solid var(--color-border);
    .icon {
      color: var(--color-primary);
    }
  }

  @keyframes borderBreath {
    0% {
      opacity: 0.1;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.1;
    }
  }

  &.opened-minapp {
    position: relative;
  }
  &.opened-minapp::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    border-radius: inherit;
    opacity: 0.3;
    border: 0.5px solid var(--color-primary);
  }
`

const StyledLink = styled.div`
  text-decoration: none;
  -webkit-app-region: none;
  &* {
    user-select: none;
  }
`

const AppsContainer = styled.div`
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  overflow-y: auto;
  overflow-x: hidden;
  margin-bottom: 10px;
  -webkit-app-region: none;
  &::-webkit-scrollbar {
    display: none;
  }
`

const Divider = styled.div`
  width: 50%;
  margin: 8px 0;
  border-bottom: 0.5px solid var(--color-border);
`

export default Sidebar

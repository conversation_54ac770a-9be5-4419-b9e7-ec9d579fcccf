import { usePreferencesStore, preferencesSelectors } from '@renderer/state/stores/preferences.store'
import type { SettingsTab } from '@renderer/state/stores/settings.store'

/**
 * 设置页面业务逻辑管理器
 * 负责协调设置相关的业务操作
 */
export class SettingsManager {
  /**
   * 切换设置选项卡
   */
  async switchTab(tab: SettingsTab): Promise<void> {
    console.log('Switching to tab:', tab)
    // @ts-expect-error - 'general' is not in UserPreferences yet
    usePreferencesStore.getState().updatePreference('general', { lastActiveSettingsTab: tab })
  }

  /**
   * 获取最后活跃的设置选项卡
   */
  async getLastActiveTab(): Promise<SettingsTab> {
    // @ts-expect-error - 'general' is not in UserPreferences yet
    const generalSettings = preferencesSelectors.all(usePreferencesStore.getState()).general
    return generalSettings?.lastActiveSettingsTab || 'shortcuts'
  }

  /**
   * 更新快捷键设置
   */
  async updateShortcut(shortcutId: string, value: string): Promise<boolean> {
    try {
      usePreferencesStore.getState().updatePreference('shortcuts', { [shortcutId]: value })
      return true
    } catch (error) {
      console.error('Failed to update shortcut:', error)
      return false
    }
  }

  /**
   * 更新主题设置
   */
  async updateTheme(themeMode: 'light' | 'dark' | 'auto'): Promise<boolean> {
    try {
      usePreferencesStore.getState().updatePreference('theme', { mode: themeMode })
      return true
    } catch (error) {
      console.error('Failed to update theme:', error)
      return false
    }
  }

  /**
   * 清除应用缓存
   */
  async clearCache(): Promise<boolean> {
    try {
      // TODO: 'recentPlays' API is not available on window.api.
      // It might have been removed or renamed.
      // if (window.api?.recentPlays) {
      //   await window.api.recentPlays.clearRecentPlays()
      // }

      // 清除 localStorage
      localStorage.clear()

      // 清除 sessionStorage
      sessionStorage.clear()

      // 清除 IndexedDB（如果使用）
      if ('indexedDB' in window) {
        const databases = await indexedDB.databases()
        await Promise.all(
          databases.map((db) => {
            if (db.name) {
              return new Promise<void>((resolve, reject) => {
                const deleteReq = indexedDB.deleteDatabase(db.name!)
                deleteReq.onsuccess = () => resolve()
                deleteReq.onerror = () => reject(deleteReq.error)
              })
            }
            return Promise.resolve()
          })
        )
      }

      // 如果有 Electron 环境，可以添加更多清理逻辑
      if (window.electron?.ipcRenderer) {
        // 通知主进程清理缓存
        try {
          await window.electron.ipcRenderer.invoke('clear-cache')
        } catch (error) {
          console.warn('Main process cache clear not available:', error)
        }
      }

      console.log('Cache cleared successfully')
      return true
    } catch (error) {
      console.error('Failed to clear cache:', error)
      return false
    }
  }

  /**
   * 导出用户数据
   */
  async exportData(): Promise<string | null> {
    try {
      const data = usePreferencesStore.getState().exportPreferences()
      return JSON.stringify(data, null, 2)
    } catch (error) {
      console.error('Failed to export data:', error)
      return null
    }
  }

  /**
   * 导入用户数据
   */
  async importData(data: string): Promise<boolean> {
    try {
      const parsedData = JSON.parse(data)
      return await usePreferencesStore.getState().importPreferences(parsedData)
    } catch (error) {
      console.error('Failed to import data:', error)
      return false
    }
  }

  /**
   * 重置所有设置
   */
  async resetAllSettings(): Promise<boolean> {
    try {
      usePreferencesStore.getState().resetAll()
      return true
    } catch (error) {
      console.error('Failed to reset settings:', error)
      return false
    }
  }

  /**
   * 检查应用更新
   */
  async checkForUpdates(): Promise<{ hasUpdate: boolean; version?: string; error?: string }> {
    try {
      // 使用现有的更新 API
      if (window.api?.update) {
        const updateResponse = await window.api.update.checkForUpdates({ silent: false })

        if (updateResponse.status === 'available' && updateResponse.info) {
          return {
            hasUpdate: true,
            version: updateResponse.info.version
          }
        } else if (updateResponse.status === 'not-available') {
          // 获取当前版本
          const currentVersion = await window.api.update.getAppVersion()
          return {
            hasUpdate: false,
            version: currentVersion
          }
        } else if (updateResponse.status === 'error') {
          return {
            hasUpdate: false,
            error: updateResponse.error || '更新检查时发生错误'
          }
        } else {
          return {
            hasUpdate: false,
            error: '未知的更新状态'
          }
        }
      } else {
        console.warn('Update API not available')
        return {
          hasUpdate: false,
          error: '更新检查功能不可用'
        }
      }
    } catch (error) {
      console.error('Failed to check for updates:', error)
      return {
        hasUpdate: false,
        error: error instanceof Error ? error.message : '更新检查失败'
      }
    }
  }
}

// 工厂函数
export const createSettingsManager = (): SettingsManager => {
  return new SettingsManager()
}

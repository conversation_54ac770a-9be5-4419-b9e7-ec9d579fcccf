// import '@renderer/databases'

import { FC, useMemo } from 'react'
import { HashRouter, Route, Routes } from 'react-router-dom'

import Sidebar from '@renderer/components/app/Sidebar'
import { SettingsPage } from '@renderer/pages'
import NavigationHandler from './infrastructure/handler/NavigationHandler'

const Router: FC = () => {
  const routes = useMemo(() => {
    return (
      <Routes>
        <Route path="/" element={<SettingsPage />} />
        <Route path="/settings/*" element={<SettingsPage />} />
      </Routes>
    )
  }, [])

  return (
    <HashRouter>
      <Sidebar />
      {routes}
      <NavigationHandler />
    </HashRouter>
  )
}

export default Router

import React, { useState } from 'react'
import { Card, Typography, Button, Space, Divider, Alert, message, Modal } from 'antd'
import {
  DatabaseOutlined,
  DeleteOutlined,
  DownloadOutlined,
  UploadOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import { createSettingsManager } from '@renderer/business/managers/settings.manager'
import { useTheme } from '@renderer/contexts'

const { Text, Title } = Typography

export function DataSettings(): React.JSX.Element {
  const { styles } = useTheme()
  const [isLoading, setIsLoading] = useState({
    clearCache: false,
    exportData: false,
    importData: false,
    resetSettings: false
  })

  const settingsManager = createSettingsManager()

  const handleClearCache = async (): Promise<void> => {
    setIsLoading((prev) => ({ ...prev, clearCache: true }))
    try {
      const success = await settingsManager.clearCache()
      if (success) {
        message.success('缓存清除成功！')
      } else {
        message.error('缓存清除失败，请稍后重试。')
      }
    } catch (error) {
      console.error('Clear cache error:', error)
      message.error('缓存清除过程中出现错误。')
    } finally {
      setIsLoading((prev) => ({ ...prev, clearCache: false }))
    }
  }

  const handleExportData = async (): Promise<void> => {
    setIsLoading((prev) => ({ ...prev, exportData: true }))
    try {
      const data = await settingsManager.exportData()
      if (data) {
        // 创建下载链接
        const blob = new Blob([data], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `echolab-settings-${new Date().toISOString().split('T')[0]}.json`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)
        message.success('数据导出成功！')
      } else {
        message.error('数据导出失败，请稍后重试。')
      }
    } catch (error) {
      console.error('Export data error:', error)
      message.error('数据导出过程中出现错误。')
    } finally {
      setIsLoading((prev) => ({ ...prev, exportData: false }))
    }
  }

  const handleImportData = (): void => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.json'
    input.onchange = async (event) => {
      const file = (event.target as HTMLInputElement).files?.[0]
      if (!file) return

      setIsLoading((prev) => ({ ...prev, importData: true }))
      try {
        const content = await file.text()
        const success = await settingsManager.importData(content)
        if (success) {
          message.success('数据导入成功！页面将刷新以应用新设置。')
          window.location.reload()
        } else {
          message.error('数据导入失败，请检查文件格式。')
        }
      } catch (error) {
        console.error('Import data error:', error)
        message.error('数据导入过程中出现错误，请检查文件格式。')
      } finally {
        setIsLoading((prev) => ({ ...prev, importData: false }))
      }
    }
    input.click()
  }

  const handleResetSettings = async (): Promise<void> => {
    Modal.confirm({
      title: '确认重置',
      content: '确定要重置所有设置吗？此操作不可撤销。',
      onOk: async () => {
        setIsLoading((prev) => ({ ...prev, resetSettings: true }))
        try {
          const success = await settingsManager.resetAllSettings()
          if (success) {
            message.success('设置重置成功！页面将刷新以应用默认设置。')
            window.location.reload()
          } else {
            message.error('设置重置失败，请稍后重试。')
          }
        } catch (error) {
          console.error('Reset settings error:', error)
          message.error('设置重置过程中出现错误。')
        } finally {
          setIsLoading((prev) => ({ ...prev, resetSettings: false }))
        }
      }
    })
  }

  return (
    <Card
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <DatabaseOutlined style={{ color: styles.colors.primary }} />
          <span>数据管理</span>
        </div>
      }
      style={styles.settingsSectionCard}
    >
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 缓存管理 */}
        <div>
          <Title level={5} style={{ margin: 0, marginBottom: 16 }}>
            <DeleteOutlined style={{ marginRight: 8, color: styles.colors.primary }} />
            缓存管理
          </Title>

          <div
            style={{
              padding: '16px',
              backgroundColor: styles.colors.backgroundAlt,
              borderRadius: styles.borderRadius.md,
              border: `1px solid ${styles.colors.border}`
            }}
          >
            <Text style={{ display: 'block', marginBottom: '16px', color: styles.colors.text }}>
              清除应用缓存可以释放存储空间，但可能会影响应用性能。
            </Text>
            <Button
              onClick={handleClearCache}
              loading={isLoading.clearCache}
              type="primary"
              icon={<DeleteOutlined />}
            >
              {isLoading.clearCache ? '清除中...' : '清除缓存'}
            </Button>
          </div>
        </div>

        <Divider />

        {/* 数据导入导出 */}
        <div>
          <Title level={5} style={{ margin: 0, marginBottom: 16 }}>
            <DownloadOutlined style={{ marginRight: 8, color: styles.colors.primary }} />
            数据备份
          </Title>

          <div
            style={{
              padding: '16px',
              backgroundColor: styles.colors.backgroundAlt,
              borderRadius: styles.borderRadius.md,
              border: `1px solid ${styles.colors.border}`
            }}
          >
            <Text style={{ display: 'block', marginBottom: '16px', color: styles.colors.text }}>
              导出您的设置和播放历史，或从备份文件中恢复数据。
            </Text>
            <Space>
              <Button
                onClick={handleExportData}
                loading={isLoading.exportData}
                type="primary"
                icon={<DownloadOutlined />}
              >
                {isLoading.exportData ? '导出中...' : '导出数据'}
              </Button>
              <Button
                onClick={handleImportData}
                loading={isLoading.importData}
                icon={<UploadOutlined />}
              >
                {isLoading.importData ? '导入中...' : '导入数据'}
              </Button>
            </Space>
          </div>
        </div>

        <Divider />

        {/* 重置设置 */}
        <div>
          <Title level={5} style={{ margin: 0, marginBottom: 16 }}>
            <ReloadOutlined style={{ marginRight: 8, color: styles.colors.primary }} />
            重置设置
          </Title>

          <Alert
            message="危险操作"
            description="这将重置所有设置到默认值，此操作不可撤销。"
            type="error"
            showIcon
            style={{ marginBottom: '16px' }}
          />

          <div
            style={{
              padding: '16px',
              backgroundColor: styles.colors.backgroundAlt,
              borderRadius: styles.borderRadius.md,
              border: `1px solid ${styles.colors.border}`
            }}
          >
            <Button
              onClick={handleResetSettings}
              loading={isLoading.resetSettings}
              type="primary"
              danger
              icon={<ReloadOutlined />}
            >
              {isLoading.resetSettings ? '重置中...' : '重置所有设置'}
            </Button>
          </div>
        </div>
      </Space>
    </Card>
  )
}

export default DataSettings

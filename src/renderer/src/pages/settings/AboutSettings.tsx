import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>, Switch, Typo<PERSON>, Space, Divider, Select, Alert, Tag } from 'antd'
import {
  InfoCircleOutlined,
  SyncOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  ExperimentOutlined,
  AppstoreOutlined,
  GlobalOutlined,
  BugOutlined,
  FileTextOutlined
} from '@ant-design/icons'
import { NotificationBadge } from '@renderer/components'
import {
  useUpdateNotificationStore,
  useIsShowRedDot
} from '@renderer/state/stores/update-notification.store'
import { useTheme } from '@renderer/contexts'

const { Text, Title } = Typography
const { Option } = Select

interface UpdateSettings {
  autoUpdate: boolean
  lastChecked?: number
  updateChannel: 'stable' | 'beta' | 'alpha'
}

export function AboutSettings(): React.JSX.Element {
  const { styles } = useTheme()
  const { addNotification } = useUIStore()

  // 使用 v2 更新通知 store
  const { markUpdateAsSeen } = useUpdateNotificationStore()
  const isShowUpdateRedDot = useIsShowRedDot('update_available')

  const [appInfo, setAppInfo] = useState({
    name: 'EchoLab',
    version: '2.0.0',
    buildDate: '2025-01-10',
    electron: '28.0.0',
    chrome: '120.0.0',
    node: '18.0.0'
  })

  const [updateSettings, setUpdateSettings] = useState<UpdateSettings>({
    autoUpdate: true,
    lastChecked: 0,
    updateChannel: 'stable'
  })
  const [isCheckingForUpdates, setIsCheckingForUpdates] = useState(false)
  const [currentVersion, setCurrentVersion] = useState<string>('')

  // 组件特有样式 - 使用 v2 主题系统
  const componentStyles = {
    versionInfoContainer: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: '2rem'
    },
    versionDetails: {
      display: 'flex',
      flexDirection: 'column' as const,
      gap: '0.5rem'
    },
    versionInfo: {
      display: 'flex',
      alignItems: 'center',
      gap: '0.75rem'
    },
    updateChannelSection: {
      display: 'flex',
      flexDirection: 'column' as const,
      gap: '1rem',
      width: '100%'
    },
    updateChannelSelect: {
      width: 200
    },
    alertContainer: {
      marginTop: '1rem'
    },
    settingsRow: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      width: '100%'
    },
    settingsRowDescription: {
      display: 'flex',
      flexDirection: 'column' as const,
      gap: '0.25rem'
    },
    settingsDivider: {
      margin: '1.5rem 0',
      borderColor: styles.colors.border
    },
    settingsCard: {
      padding: '16px',
      backgroundColor: styles.colors.backgroundAlt,
      border: `1px solid ${styles.colors.border}`,
      borderRadius: styles.borderRadius.md
    },
    settingsInfoHeader: {
      display: 'flex',
      alignItems: 'center',
      gap: '0.5rem',
      marginBottom: '1rem'
    },
    settingsInfoIcon: {
      color: styles.colors.primary,
      fontSize: '1.1rem'
    },
    settingsFeatureList: {
      paddingLeft: '1.5rem',
      margin: 0
    },
    settingsFeatureListItem: {
      marginBottom: '0.5rem',
      color: styles.colors.text
    }
  }

  // 获取当前版本和设置
  useEffect(() => {
    const loadData = async (): Promise<void> => {
      try {
        const [version, settings] = await Promise.all([
          window.api.update.getAppVersion(),
          window.api.update.getUpdateSettings()
        ])
        setCurrentVersion(version)
        setUpdateSettings(settings)
        setAppInfo((prev) => ({ ...prev, version }))
      } catch (error) {
        console.error('获取应用信息失败:', error)
      }
    }

    loadData()
  }, [])

  // 检查更新
  const handleCheckForUpdates = async (): Promise<void> => {
    try {
      setIsCheckingForUpdates(true)

      // 调用更新检查，silent: false 表示用户主动检查
      const result = await window.api.update.checkForUpdates({ silent: false })

      // 只有在没有可用更新时才显示"已是最新版本"的通知
      if (result && result.status === 'not-available') {
        addNotification({
          type: 'success',
          title: '已是最新版本',
          message: '您当前使用的已经是最新版本。',
          duration: 3000
        })
      }
      // 如果有可用更新，UpdateNotification 组件会自动显示对话框
      else if (result && result.status === 'available') {
        console.log('发现可用更新，UpdateNotification 组件将自动显示更新对话框')
      }
    } catch (error) {
      console.error('检查更新失败:', error)
      addNotification({
        type: 'error',
        title: '检查更新失败',
        message: String(error),
        duration: 4000
      })
    } finally {
      setIsCheckingForUpdates(false)
    }
  }

  // 切换自动更新
  const handleToggleAutoUpdate = async (checked: boolean): Promise<void> => {
    try {
      await window.api.update.enableAutoUpdate(checked)
      const newSettings = { ...updateSettings, autoUpdate: checked }
      setUpdateSettings(newSettings)
      await window.api.update.saveUpdateSettings(newSettings)

      addNotification({
        type: 'success',
        title: checked ? '已启用自动更新' : '已禁用自动更新',
        message: checked ? '应用将在后台自动检查并提示可用更新' : '您需要手动检查更新',
        duration: 3000
      })
    } catch (error) {
      console.error('切换自动更新设置失败:', error)
      addNotification({
        type: 'error',
        title: '设置失败',
        message: String(error),
        duration: 4000
      })
    }
  }

  // 更改更新渠道
  const handleUpdateChannelChange = async (channel: 'stable' | 'beta' | 'alpha'): Promise<void> => {
    try {
      // 设置更新渠道
      await window.api.update.setUpdateChannel(channel)
      const newSettings = { ...updateSettings, updateChannel: channel }
      setUpdateSettings(newSettings)

      addNotification({
        type: 'success',
        title: '更新渠道已变更',
        message: `已切换到 ${getChannelDisplayName(channel)} 渠道，正在检查新渠道的更新...`,
        duration: 4000
      })

      // 立即触发更新检查以查找新渠道的更新
      try {
        setIsCheckingForUpdates(true)
        const result = await window.api.update.checkForUpdates({ silent: true })

        if (result && result.status === 'available') {
          addNotification({
            type: 'info',
            title: '发现新版本',
            message: `在 ${getChannelDisplayName(channel)} 渠道中发现可用更新`,
            duration: 5000
          })
        } else if (result && result.status === 'not-available') {
          addNotification({
            type: 'success',
            title: '渠道切换完成',
            message: `${getChannelDisplayName(channel)} 渠道已是最新版本`,
            duration: 3000
          })
        }
      } catch (updateCheckError) {
        console.warn('切换渠道后检查更新失败:', updateCheckError)
        // 不显示错误通知，因为渠道切换本身是成功的
      } finally {
        setIsCheckingForUpdates(false)
      }
    } catch (error) {
      console.error('更改更新渠道失败:', error)
      addNotification({
        type: 'error',
        title: '设置失败',
        message: String(error),
        duration: 4000
      })
    }
  }

  // 获取渠道显示名称
  const getChannelDisplayName = (channel: string): string => {
    switch (channel) {
      case 'stable':
        return '稳定版'
      case 'beta':
        return '测试版'
      case 'alpha':
        return '开发版'
      default:
        return '未知'
    }
  }

  // 格式化最后检查时间
  const formatLastChecked = (timestamp?: number): string => {
    if (!timestamp) return '从未检查'
    const date = new Date(timestamp)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) return '刚刚检查'
    if (diffInHours < 24) return `${diffInHours} 小时前`
    return `${Math.floor(diffInHours / 24)} 天前`
  }

  const handleOpenLicense = (): void => {
    // 打开许可证URL
    if (window.electron?.ipcRenderer) {
      window.electron.ipcRenderer.invoke('open-external', 'https://opensource.org/licenses/MIT')
    } else {
      // 回退方案
      window.open('https://opensource.org/licenses/MIT', '_blank')
    }
  }

  const handleOpenHomepage = (): void => {
    // 打开官网
    if (window.electron?.ipcRenderer) {
      window.electron.ipcRenderer.invoke('open-external', 'https://github.com/your-org/echolab')
    } else {
      // 回退方案
      window.open('https://github.com/your-org/echolab', '_blank')
    }
  }

  const handleReportIssue = (): void => {
    // 打开问题追踪页面
    if (window.electron?.ipcRenderer) {
      window.electron.ipcRenderer.invoke(
        'open-external',
        'https://github.com/your-org/echolab/issues'
      )
    } else {
      // 回退方案
      window.open('https://github.com/your-org/echolab/issues', '_blank')
    }
  }

  return (
    <Card
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <InfoCircleOutlined style={{ color: styles.colors.primary }} />
          <span>关于 EchoLab</span>
        </div>
      }
      style={styles.settingsSectionCard}
    >
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 应用更新 */}
        <div>
          <Title level={5} style={{ margin: 0, marginBottom: 16 }}>
            <SyncOutlined style={{ marginRight: 8, color: styles.colors.primary }} />
            应用更新
          </Title>

          <div style={componentStyles.settingsCard}>
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              {/* 版本信息 */}
              <div style={componentStyles.versionInfoContainer}>
                <div style={componentStyles.versionDetails}>
                  <div style={componentStyles.versionInfo}>
                    <Text strong>当前版本:</Text>
                    <Tag color="blue">{currentVersion || '未知'}</Tag>
                  </div>
                  <Text type="secondary">
                    上次检查: {formatLastChecked(updateSettings.lastChecked)}
                  </Text>
                </div>

                <NotificationBadge showDot={isShowUpdateRedDot} offset={[-8, 8]}>
                  <Button
                    type="primary"
                    icon={<SyncOutlined spin={isCheckingForUpdates} />}
                    loading={isCheckingForUpdates}
                    onClick={() => {
                      handleCheckForUpdates()
                      // 用户点击检查更新时，标记为已查看
                      if (isShowUpdateRedDot) {
                        markUpdateAsSeen()
                      }
                    }}
                    style={{ marginLeft: '0.5rem', borderRadius: styles.borderRadius.md }}
                  >
                    检查更新
                  </Button>
                </NotificationBadge>
              </div>

              <Divider style={componentStyles.settingsDivider} />

              {/* 自动更新设置 */}
              <div style={componentStyles.settingsRow}>
                <div style={componentStyles.settingsRowDescription}>
                  <Text strong>自动检查更新</Text>
                  <Text type="secondary">应用将定期在后台检查更新并通知您</Text>
                </div>
                <Switch checked={updateSettings.autoUpdate} onChange={handleToggleAutoUpdate} />
              </div>

              <Divider style={componentStyles.settingsDivider} />

              {/* 更新渠道设置 */}
              <div style={componentStyles.updateChannelSection}>
                <Text strong>更新渠道</Text>
                <Select
                  value={updateSettings.updateChannel}
                  onChange={handleUpdateChannelChange}
                  style={componentStyles.updateChannelSelect}
                  loading={isCheckingForUpdates}
                  disabled={isCheckingForUpdates}
                >
                  <Option value="stable">
                    <Space>
                      <CheckCircleOutlined style={{ color: styles.colors.primary }} />
                      稳定版
                    </Space>
                  </Option>
                  <Option value="beta">
                    <Space>
                      <WarningOutlined style={{ color: '#faad14' }} />
                      测试版
                    </Space>
                  </Option>
                  <Option value="alpha">
                    <Space>
                      <ExperimentOutlined style={{ color: '#ff4d4f' }} />
                      开发版
                    </Space>
                  </Option>
                </Select>

                <Alert
                  message={
                    <Space>
                      <Text>
                        当前使用 {getChannelDisplayName(updateSettings.updateChannel)} 渠道
                      </Text>
                    </Space>
                  }
                  description={
                    updateSettings.updateChannel === 'stable'
                      ? '推荐用于日常使用，稳定性最高，更新频率较低'
                      : updateSettings.updateChannel === 'beta'
                        ? '包含新功能的预发布版本，可能存在一些已知问题'
                        : '包含最新功能的开发版本，仅供测试使用，可能不稳定'
                  }
                  type={
                    updateSettings.updateChannel === 'stable'
                      ? 'success'
                      : updateSettings.updateChannel === 'beta'
                        ? 'warning'
                        : 'error'
                  }
                  showIcon
                  style={componentStyles.alertContainer}
                />
              </div>

              <Divider style={componentStyles.settingsDivider} />

              {/* 更新说明 */}
              <div>
                <div style={componentStyles.settingsInfoHeader}>
                  <InfoCircleOutlined style={componentStyles.settingsInfoIcon} />
                  <Text strong>更新功能说明：</Text>
                </div>
                <ul style={componentStyles.settingsFeatureList}>
                  <li style={componentStyles.settingsFeatureListItem}>
                    <Text>
                      <strong>增量更新：</strong> 只下载变更部分，节省带宽和时间
                    </Text>
                  </li>
                  <li style={componentStyles.settingsFeatureListItem}>
                    <Text>
                      <strong>安全验证：</strong> 更新包经过数字签名验证，确保安全可靠
                    </Text>
                  </li>
                  <li style={componentStyles.settingsFeatureListItem}>
                    <Text>
                      <strong>自动重启：</strong> 更新完成后，应用将自动重启以应用新版本
                    </Text>
                  </li>
                  <li style={componentStyles.settingsFeatureListItem}>
                    <Text>
                      <strong>回滚保护：</strong> 支持在更新失败时自动回滚到之前版本
                    </Text>
                  </li>
                </ul>
              </div>
            </Space>
          </div>
        </div>

        <Divider />

        {/* 应用信息 */}
        <div>
          <Title level={5} style={{ margin: 0, marginBottom: 16 }}>
            <AppstoreOutlined style={{ marginRight: 8, color: styles.colors.primary }} />
            应用信息
          </Title>

          <div
            style={{
              padding: '1.5rem',
              backgroundColor: styles.colors.backgroundAlt,
              borderRadius: styles.borderRadius.md,
              border: `1px solid ${styles.colors.border}`
            }}
          >
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                marginBottom: '1rem'
              }}
            >
              <div
                style={{
                  width: '64px',
                  height: '64px',
                  backgroundColor: styles.colors.primary,
                  borderRadius: styles.borderRadius.md,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: '1rem',
                  fontSize: '24px',
                  color: styles.colors.textInverse,
                  fontWeight: 'bold'
                }}
              >
                E
              </div>
              <div>
                <h4 style={{ margin: '0 0 0.25rem 0', fontSize: '1.25rem' }}>{appInfo.name}</h4>
                <p style={{ margin: 0, color: styles.colors.text, opacity: 0.7 }}>
                  智能视频学习助手
                </p>
              </div>
            </div>

            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '0.5rem' }}>
              <div>
                <strong>版本:</strong> {appInfo.version}
              </div>
              <div>
                <strong>构建日期:</strong> {appInfo.buildDate}
              </div>
              <div>
                <strong>Electron:</strong> {appInfo.electron}
              </div>
              <div>
                <strong>Chrome:</strong> {appInfo.chrome}
              </div>
              <div style={{ gridColumn: 'span 2' }}>
                <strong>Node.js:</strong> {appInfo.node}
              </div>
            </div>
          </div>
        </div>

        <Divider />

        {/* 链接和支持 */}
        <div>
          <Title level={5} style={{ margin: 0, marginBottom: 16 }}>
            <GlobalOutlined style={{ marginRight: 8, color: styles.colors.primary }} />
            链接和支持
          </Title>

          <div
            style={{
              padding: '1rem',
              backgroundColor: styles.colors.backgroundAlt,
              borderRadius: styles.borderRadius.md,
              border: `1px solid ${styles.colors.border}`
            }}
          >
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Button
                onClick={handleOpenHomepage}
                type="text"
                icon={<GlobalOutlined />}
                style={{
                  width: '100%',
                  textAlign: 'left',
                  justifyContent: 'flex-start',
                  height: 'auto',
                  padding: '8px 16px'
                }}
              >
                官方网站
              </Button>

              <Button
                onClick={handleReportIssue}
                type="text"
                icon={<BugOutlined />}
                style={{
                  width: '100%',
                  textAlign: 'left',
                  justifyContent: 'flex-start',
                  height: 'auto',
                  padding: '8px 16px'
                }}
              >
                报告问题
              </Button>

              <Button
                onClick={handleOpenLicense}
                type="text"
                icon={<FileTextOutlined />}
                style={{
                  width: '100%',
                  textAlign: 'left',
                  justifyContent: 'flex-start',
                  height: 'auto',
                  padding: '8px 16px'
                }}
              >
                开源许可证
              </Button>
            </Space>
          </div>
        </div>

        <Divider />

        {/* 版权信息 */}
        <div>
          <div
            style={{
              padding: '1rem',
              backgroundColor: styles.colors.backgroundAlt,
              borderRadius: styles.borderRadius.md,
              border: `1px solid ${styles.colors.border}`,
              textAlign: 'center'
            }}
          >
            <Text
              style={{
                display: 'block',
                color: styles.colors.text,
                opacity: 0.7,
                fontSize: '0.875rem'
              }}
            >
              © 2025 EchoLab. 保留所有权利。
            </Text>
            <Text
              style={{
                display: 'block',
                marginTop: '0.5rem',
                color: styles.colors.text,
                opacity: 0.7,
                fontSize: '0.875rem'
              }}
            >
              基于 MIT 许可证开源
            </Text>
          </div>
        </div>
      </Space>
    </Card>
  )
}

export default AboutSettings

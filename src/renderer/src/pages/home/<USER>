/**
 * HomePage组件类型定义 / HomePage Component Type Definitions
 *
 * 定义HomePage组件相关的所有TypeScript接口和类型
 * Defines all TypeScript interfaces and types related to the HomePage component
 */

import { RecentPlayItem } from '@types_/domain'

/**
 * HomePage组件的属性接口 / HomePage Component Props Interface
 */
export interface HomePageProps {
  /**
   * 导航到播放页面的回调函数 / Callback to navigate to play page
   */
  onNavigateToPlay: () => void
}

/**
 * 视频卡片状态 / Video Card State
 */
export interface VideoCardState {
  /**
   * 是否处于加载状态 / Whether in loading state
   */
  isLoading: boolean

  /**
   * 是否显示删除确认对话框 / Whether to show delete confirmation dialog
   */
  showDeleteConfirm: boolean

  /**
   * 选中的文件ID / Selected file ID
   */
  selectedFileId: string

  /**
   * 选中的文件名 / Selected file name
   */
  selectedFileName: string
}

/**
 * 文件选择状态 / File Selection State
 */
export interface FileSelectionState {
  /**
   * 是否正在选择文件 / Whether currently selecting file
   */
  isSelectingFile: boolean

  /**
   * 选择进度 / Selection progress
   */
  progress?: number
}

/**
 * 模态框状态 / Modal State
 */
export interface ModalState {
  /**
   * 删除确认模态框是否打开 / Whether delete confirmation modal is open
   */
  isDeleteModalOpen: boolean

  /**
   * 清空确认模态框是否打开 / Whether clear confirmation modal is open
   */
  isClearModalOpen: boolean
}

/**
 * HomePage内部状态接口 / HomePage Internal State Interface
 */
export interface HomePageState extends VideoCardState, FileSelectionState, ModalState {
  /**
   * 错误消息 / Error message
   */
  error?: string
}

/**
 * 主题模式类型 / Theme Mode Type
 */
export type ThemeMode = 'default' | 'compact'

/**
 * 视频网格列配置 / Video Grid Column Configuration
 */
export interface GridColumnConfig {
  /**
   * 中等屏幕 / Medium screens
   */
  md: number

  /**
   * 大屏幕 / Large screens
   */
  lg: number

  /**
   * 超大屏幕 / Extra large screens
   */
  xl: number

  /**
   * 特大屏幕 / XXL screens
   */
  xxl?: number
}

/**
 * 响应式间距配置 / Responsive Spacing Configuration
 */
export interface ResponsiveSpacing {
  /**
   * 水平间距 / Horizontal spacing
   */
  horizontal: number

  /**
   * 垂直间距 / Vertical spacing
   */
  vertical: number
}

/**
 * HomePage组件事件处理器 / HomePage Component Event Handlers
 */
export interface HomePageHandlers {
  /**
   * 处理视频文件选择 / Handle video file selection
   */
  handleVideoFileSelect: () => Promise<boolean>

  /**
   * 处理打开最近播放项目 / Handle opening recent play item
   */
  handleOpenResource: (item: RecentPlayItem) => Promise<boolean>

  /**
   * 处理删除最近播放项目 / Handle removing recent play item
   */
  handleRemoveResource: (id: string) => Promise<void>

  /**
   * 处理清空最近播放列表 / Handle clearing recent play list
   */
  handleClearResources: () => void

  /**
   * 显示删除确认对话框 / Show delete confirmation dialog
   */
  showDeleteConfirm: (id: string, fileName: string) => void
}

/**
 * 导出所有类型 / Export all types
 */
export type { HomePageProps as default }

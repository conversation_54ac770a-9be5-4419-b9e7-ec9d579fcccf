import React, { useCallback } from 'react'
import { Button, Typography, Spin, message } from 'antd'
import { PlusOutlined, HeartOutlined, HeartFilled, DeleteOutlined } from '@ant-design/icons'

// v2架构导入 / v2 Architecture Imports
import type { HomePageProps } from './HomePage.types'
import { useTheme } from '@renderer/contexts'

const { Title } = Typography

/**
 * HomePage函数式组件 / HomePage Functional Component
 *
 * 使用React.FC类型，遵循TypeScript严格模式
 * Uses React.FC type, adheres to TypeScript strict mode
 */
const HomePage: React.FC<HomePageProps> = ({ onNavigateToPlay }) => {
  // v2架构状态管理 / v2 Architecture State Management
  const [state, actions] = useHomePage({
    autoLoadRecentPlays: true,
    autoLoadPreferences: true,
    recentPlaysQuery: {
      limit: 20,
      sortBy: 'lastPlayedAt',
      sortOrder: 'desc'
    },
    debug: process.env.NODE_ENV === 'development'
  })

  const { styles } = useTheme()

  // 记忆化计算的样式类名 / Memoized style class names
  // const containerClassName = useMemo(() => {
  //   return `${styles.homePage} ${state.themeMode === 'compact' ? styles.compact : ''}`
  // }, [state.themeMode])

  // 处理视频文件选择 / Handle video file selection
  const handleVideoFileSelect = useCallback(async (): Promise<boolean> => {
    try {
      const result = await actions.selectVideoFile({
        autoFindSubtitles: true,
        generateThumbnail: true
      })

      if (result.success && result.videoFile) {
        message.success('视频文件添加成功！')

        // 导航到播放页面 / Navigate to play page
        onNavigateToPlay()
        return true
      } else {
        if (result.error && result.error !== '用户取消了文件选择') {
          message.error(result.error)
        }
        return false
      }
    } catch (error) {
      console.error('❌ 视频文件选择失败:', error)
      message.error('文件选择失败，请重试')
      return false
    }
  }, [actions, onNavigateToPlay])

  // 处理打开最近播放项目 / Handle opening recent play item
  const handleOpenResource = useCallback(
    async (item: (typeof state.recentPlays)[0]): Promise<boolean> => {
      try {
        console.log('🎬 打开最近播放项目:', item.fileName)

        // 更新播放记录 / Update play record
        await actions.toggleFavorite(item.id) // 这里可以改为更新播放时间的逻辑

        // 导航到播放页面 / Navigate to play page
        onNavigateToPlay()

        return true
      } catch (error) {
        console.error('❌ 打开资源失败:', error)
        message.error('打开视频失败')
        return false
      }
    },
    [actions, onNavigateToPlay]
  )

  // 处理删除播放项目 / Handle removing play item
  const handleRemoveResource = useCallback(async (): Promise<void> => {
    if (!state.selectedFileId) return

    try {
      const success = await actions.deleteRecentPlay(state.selectedFileId)

      if (success) {
        message.success('删除成功')
      } else {
        message.error('删除失败')
      }
    } catch (error) {
      console.error('❌ 删除资源失败:', error)
      message.error('删除失败')
    }
  }, [actions, state.selectedFileId])

  // 处理清空最近播放列表 / Handle clearing recent play list
  const handleClearResources = useCallback(async (): Promise<void> => {
    try {
      const success = await actions.clearAllRecentPlays()

      if (success) {
        message.success('清空成功')
      } else {
        message.error('清空失败')
      }
    } catch (error) {
      console.error('❌ 清空失败:', error)
      message.error('清空失败')
    }
  }, [actions])

  // 处理收藏切换 / Handle favorite toggle
  const handleToggleFavorite = useCallback(
    async (itemId: string): Promise<void> => {
      try {
        const newStatus = await actions.toggleFavorite(itemId)
        message.success(newStatus ? '已添加到收藏' : '已从收藏移除')
      } catch (error) {
        console.error('❌ 切换收藏状态失败:', error)
        message.error('操作失败')
      }
    },
    [actions]
  )

  // 渲染空状态 / Render empty state
  const renderEmptyState = useCallback((): React.ReactNode => {
    return (
      <div className={styles.loadingContainer}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '64px', marginBottom: '16px', opacity: 0.6 }}>📺</div>
          <Title level={4} style={{ marginBottom: '8px' }}>
            还没有观看过任何视频
          </Title>
          <p style={{ color: '#8c8c8c', marginBottom: '24px' }}>点击下方按钮添加您的第一个视频</p>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleVideoFileSelect}
            loading={state.isSelectingFile}
            disabled={state.isSelectingFile}
            style={{ borderRadius: '8px' }}
          >
            {state.isSelectingFile ? '选择视频中...' : '立即添加'}
          </Button>
        </div>
      </div>
    )
  }, [handleVideoFileSelect, state.isSelectingFile])

  // 渲染加载状态 / Render loading state
  const renderLoadingState = useCallback((): React.ReactNode => {
    return (
      <div>
        <Spin size="large" tip="加载最近播放记录中..." />
      </div>
    )
  }, [])

  // 渲染视频卡片 / Render video card
  const renderVideoCard = useCallback(
    (item: (typeof state.recentPlays)[0]) => {
      if (!item) return null

      const formatDuration = (seconds: number): string => {
        const hours = Math.floor(seconds / 3600)
        const minutes = Math.floor((seconds % 3600) / 60)
        const secs = Math.floor(seconds % 60)

        if (hours > 0) {
          return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
        }
        return `${minutes}:${secs.toString().padStart(2, '0')}`
      }

      const formatFileSize = (bytes: number): string => {
        const sizes = ['B', 'KB', 'MB', 'GB']
        let i = 0
        while (bytes >= 1024 && i < sizes.length - 1) {
          bytes /= 1024
          i++
        }
        return `${bytes.toFixed(1)} ${sizes[i]}`
      }

      return (
        <div
          key={item.id}
          onClick={() => handleOpenResource(item)}
          style={{
            cursor: 'pointer',
            border: '1px solid #f0f0f0',
            borderRadius: '8px',
            padding: '12px',
            margin: '8px',
            transition: 'all 0.3s ease',
            backgroundColor: '#fff'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)'
            e.currentTarget.style.transform = 'translateY(-2px)'
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.boxShadow = 'none'
            e.currentTarget.style.transform = 'translateY(0)'
          }}
        >
          {/* 缩略图区域 / Thumbnail area */}
          <div
            style={{
              width: '100%',
              height: '120px',
              backgroundColor: '#f5f5f5',
              borderRadius: '6px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: '8px',
              position: 'relative',
              overflow: 'hidden'
            }}
          >
            {item.thumbnailPath ? (
              <img
                src={item.thumbnailPath}
                alt={item.fileName}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover'
                }}
              />
            ) : (
              <div style={{ fontSize: '32px', color: '#d9d9d9' }}>🎬</div>
            )}

            {/* 时长标签 / Duration label */}
            <div
              style={{
                position: 'absolute',
                bottom: '4px',
                right: '4px',
                backgroundColor: 'rgba(0,0,0,0.7)',
                color: 'white',
                padding: '2px 6px',
                borderRadius: '4px',
                fontSize: '12px'
              }}
            >
              {formatDuration(item.duration)}
            </div>
          </div>

          {/* 文件信息 / File information */}
          <div style={{ marginBottom: '8px' }}>
            <div
              style={{
                fontWeight: 'bold',
                fontSize: '14px',
                marginBottom: '4px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}
              title={item.fileName}
            >
              {item.fileName}
            </div>

            <div style={{ fontSize: '12px', color: '#8c8c8c', marginBottom: '4px' }}>
              大小: {formatFileSize(item.fileSize)} • 播放 {item.playCount} 次
            </div>

            <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
              {new Date(item.lastPlayedAt).toLocaleDateString('zh-CN', {
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </div>
          </div>

          {/* 操作按钮 / Action buttons */}
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Button
              type="text"
              size="small"
              icon={
                item.isFavorite ? <HeartFilled style={{ color: '#ff4d4f' }} /> : <HeartOutlined />
              }
              onClick={(e) => {
                e.stopPropagation()
                handleToggleFavorite(item.id)
              }}
              style={{ padding: '4px' }}
            />

            <Button
              type="text"
              size="small"
              icon={<DeleteOutlined />}
              onClick={(e) => {
                e.stopPropagation()
                actions.openDeleteDialog(item.id, item.fileName)
              }}
              style={{ padding: '4px', color: '#ff4d4f' }}
            />
          </div>
        </div>
      )
    },
    [handleOpenResource, handleToggleFavorite, actions]
  )

  // 渲染视频网格 / Render video grid
  const renderVideoGrid = useCallback((): React.ReactNode => {
    return (
      <div
        style={{
          display: 'grid',
          gridTemplateColumns: `repeat(auto-fill, minmax(200px, 1fr))`,
          gap: '16px',
          padding: '16px 0'
        }}
      >
        {state.recentPlays.map((item) => renderVideoCard(item))}
      </div>
    )
  }, [state.recentPlays, renderVideoCard])

  // 渲染主要内容 / Render main content
  const renderMainContent = useCallback((): React.ReactNode => {
    if (state.isLoadingRecentPlays) {
      return renderLoadingState()
    }

    if (state.recentPlays.length === 0) {
      return renderEmptyState()
    }

    return <div className={styles.videoGridContainer}>{renderVideoGrid()}</div>
  }, [
    state.isLoadingRecentPlays,
    state.recentPlays.length,
    renderLoadingState,
    renderEmptyState,
    renderVideoGrid
  ])

  return (
    <div>
      <div className={styles.contentContainer}>
        {/* 头部区域 / Header Section */}
        <div className={styles.headerSection}>
          <div className={styles.titleContainer}>
            <Title level={3} className={styles.title}>
              最近观看
            </Title>
            {!state.isLoadingRecentPlays && (
              <div className={styles.countBadge}>{state.totalRecentPlays}</div>
            )}
          </div>

          <div className={styles.actionButtonsContainer}>
            {!state.isLoadingRecentPlays && state.recentPlays.length > 0 && (
              <Button
                type="text"
                size="small"
                onClick={handleClearResources}
                style={{ color: '#8c8c8c' }}
              >
                清空
              </Button>
            )}
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleVideoFileSelect}
              loading={state.isSelectingFile}
              disabled={state.isSelectingFile}
              style={{ borderRadius: '8px' }}
            >
              {state.isSelectingFile ? '选择视频中...' : '添加视频'}
            </Button>
          </div>
        </div>

        {/* 主要内容区域 / Main Content Area */}
        <div className={styles.mainContent}>{renderMainContent()}</div>
      </div>

      {/* TODO: 添加确认模态框组件 / TODO: Add confirmation modal components */}
      {/*
      <ConfirmModals
        isDeleteModalOpen={state.isDeleteModalOpen}
        selectedFileName={state.selectedFileName}
        onDeleteCancel={() => setState(prev => ({ ...prev, isDeleteModalOpen: false }))}
        onDeleteConfirm={() => handleRemoveResource(state.selectedFileId)}
        isClearModalOpen={state.isClearModalOpen}
        recentPlaysCount={recentPlays.length}
        onClearCancel={() => setState(prev => ({ ...prev, isClearModalOpen: false }))}
        onClearConfirm={handleClearResources}
      />
      */}
    </div>
  )
}

// 设置显示名称 / Set display name
HomePage.displayName = 'HomePage'

export default HomePage

// 导出类型 / Export types
export type { HomePageProps } from './HomePage.types'

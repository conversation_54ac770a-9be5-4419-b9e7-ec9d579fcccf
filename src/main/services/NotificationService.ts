import { <PERSON><PERSON>erWindow, Notification as ElectronNotification } from 'electron'
import { Notification } from '@types'

class NotificationService {
  private window: BrowserWindow

  constructor(window: <PERSON>rowserWindow) {
    // Initialize the service
    this.window = window
  }

  public async sendNotification(notification: Notification) {
    // 使用 Electron Notification API
    const electronNotification = new ElectronNotification({
      title: notification.title,
      body: notification.message
    })

    electronNotification.on('click', () => {
      this.window.show()
      this.window.webContents.send('notification-click', notification)
    })

    electronNotification.show()
  }
}

export default NotificationService

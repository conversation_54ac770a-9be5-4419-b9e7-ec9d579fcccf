# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

EchoLab is an Electron desktop application designed for language learners, providing sentence-by-sentence intensive listening functionality for video content. The app supports multiple video formats (MP4, AVI, MKV, MOV, WMV, FLV) and subtitle formats (SRT, VTT, ASS/SSA, JSON).

## Essential Commands

### Development
```bash
# Development server with hot reload
pnpm dev

# Build for production (includes typecheck)
pnpm build

# Preview built application
pnpm start
```

### Code Quality & Testing
```bash
# Type checking (must pass before commits)
pnpm typecheck              # Check all code
pnpm typecheck:node         # Check main process only
pnpm typecheck:web          # Check renderer process only

# Linting and formatting (automatically runs on commit)
pnpm lint                   # ESLint check
pnpm lint:fix               # Auto-fix ESLint issues
pnpm format                 # Format with Prettier
pnpm format:check           # Check formatting

# Testing
pnpm test                   # Unit tests (Vitest)
pnpm test:ui                # Interactive test UI
pnpm test:coverage          # Test coverage report
pnpm test:e2e               # End-to-end tests (Playwright)
pnpm test:e2e:ui            # E2E test UI
pnpm test:e2e:debug         # Debug E2E tests
```

### Building Releases
```bash
# Platform-specific builds
pnpm build:win              # Windows
pnpm build:mac              # macOS
pnpm build:linux            # Linux
```

## Architecture Overview

### Electron Structure
- **Main Process** (`src/main/`): Node.js backend, window management, file system operations
- **Preload Scripts** (`src/preload/`): Secure IPC bridge between main and renderer
- **Renderer Process** (`src/renderer/src/`): React frontend application

### V2 Architecture Migration
The project is migrating to a clean 5-layer architecture in `src/renderer/src/v2/`:

1. **Infrastructure Layer** (`infrastructure/`): Types, constants, utilities, base hooks
2. **Services Layer** (`services/`): External APIs, storage, persistence
3. **Business Layer** (`business/`): Domain logic, managers, validators
4. **State Layer** (`state/`): Zustand stores, actions, selectors
5. **Presentation Layer** (`presentation/`): UI components, pages

**Important**: All new features must use the V2 architecture. Legacy code in the main `src/renderer/src/` directories should not be extended.

### Key Directories
```
src/
├── main/                   # Electron main process
│   ├── handlers/          # IPC handlers by feature
│   ├── utils/             # Utilities (logger, version parser)
│   └── window/            # Window management
├── preload/               # Secure IPC bridges
├── renderer/src/          # React application
│   ├── v2/                # New clean architecture
│   │   ├── infrastructure/
│   │   ├── services/
│   │   ├── business/
│   │   ├── state/
│   │   └── presentation/
│   ├── components/        # Legacy UI components
│   ├── stores/           # Legacy Zustand stores
│   ├── hooks/            # Legacy React hooks
│   └── utils/            # Legacy utilities
└── types/                 # Shared TypeScript types
```

## Development Guidelines

### Code Quality Requirements
- **TypeScript**: All new code must use strict TypeScript
- **No `any` types**: Use proper type definitions
- **Explicit return types**: Required for all functions
- **ESLint compliance**: All rules must pass
- **Prettier formatting**: Automatic via pre-commit hooks

### V2 Architecture Development
1. **Start with types**: Define interfaces in `infrastructure/types/`
2. **Build services**: Implement external integrations in `services/`
3. **Add business logic**: Create managers and validators in `business/`
4. **Manage state**: Use Zustand stores in `state/`
5. **Create UI**: Build components in `presentation/`

### Testing Strategy
- **Unit tests**: Vitest with jsdom environment
- **E2E tests**: Playwright for Electron app testing
- **Coverage**: Minimum coverage requirements enforced
- **Test files**: `.test.ts/.test.tsx` for unit tests, `.e2e.ts` for E2E

### Path Aliases
```typescript
// Available in both main and renderer processes
'@renderer/*'  → 'src/renderer/src/*'
'@types_/*'    → 'src/types/*'
```

## Key Features & Components

### Video Player Core
- **VideoPlayer**: Main video playback component with subtitle overlay
- **Subtitle System**: Multi-format subtitle support with BilingualSubtitle
- **Controls**: Playback controls, speed adjustment, fullscreen mode
- **Loop System**: Sentence-by-sentence loop functionality

### State Management
- **Zustand stores**: Centralized state for video, subtitles, settings
- **V2 state**: Clean state management in `v2/state/`
- **Persistence**: Electron-conf for settings storage

### File Handling
- **Video formats**: MP4, AVI, MKV, MOV, WMV, FLV support
- **Subtitle formats**: SRT, VTT, ASS/SSA, JSON parsing
- **Recent files**: Persistent recent video list

### Settings & Configuration
- **Appearance**: Theme customization, window frame options
- **Shortcuts**: Configurable keyboard shortcuts
- **Updates**: Auto-update system with notifications
- **Third-party services**: Dictionary integration, OpenAI support

## Common Development Tasks

### Adding a New Feature
1. Create types in `src/renderer/src/v2/infrastructure/types/`
2. Implement services in `src/renderer/src/v2/services/`
3. Add business logic in `src/renderer/src/v2/business/`
4. Create Zustand store in `src/renderer/src/v2/state/`
5. Build UI components in `src/renderer/src/v2/presentation/`
6. Write tests following existing patterns

### Working with IPC
- **Main handlers**: Add to `src/main/handlers/` by feature area
- **Preload**: Expose APIs via preload scripts
- **Renderer**: Use IPC through preload context

### Debugging
- **Main process**: Use electron-log for logging
- **Renderer process**: Standard browser DevTools
- **IPC communication**: Enable with debug flags

### Performance Considerations
- **React optimization**: Use memo, useMemo, useCallback appropriately
- **Subtitle rendering**: Virtual scrolling for large subtitle lists
- **Video performance**: Proper cleanup and event handling

## Build & Release Process

### Pre-commit Hooks
Automatically runs via Husky:
- ESLint with auto-fix
- Prettier formatting
- TypeScript checking

### CI/CD
- **Type checking**: Must pass for all builds
- **Testing**: Unit and E2E tests required
- **Build verification**: All platforms tested

### Release
- **Versioning**: Semantic versioning with prerelease support
- **Artifacts**: Platform-specific installers
- **Updates**: Electron-updater for auto-updates

## Important Files

### Configuration
- `electron.vite.config.ts`: Build configuration with aliases
- `package.json`: Dependencies and scripts
- `tsconfig.json`: TypeScript project references
- `eslint.config.mjs`: ESLint rules and React configuration
- `vitest.config.ts`: Test configuration
- `playwright.config.ts`: E2E test configuration

### Entry Points
- `src/main/index.ts`: Electron main process entry
- `src/renderer/src/main.tsx`: React application entry
- `src/renderer/src/v2/index.ts`: V2 architecture exports

## Troubleshooting

### Common Issues
- **Build failures**: Run `pnpm typecheck` to identify TypeScript errors
- **Test failures**: Check test setup in `src/test/setup.ts`
- **E2E issues**: Verify Electron app builds before running E2E tests

### Performance Problems
- **Slow startup**: Check main process initialization
- **UI lag**: Profile React components for unnecessary rerenders
- **Memory leaks**: Ensure proper cleanup in useEffect hooks

Remember: Always use the V2 architecture for new features and maintain strict TypeScript standards throughout development.